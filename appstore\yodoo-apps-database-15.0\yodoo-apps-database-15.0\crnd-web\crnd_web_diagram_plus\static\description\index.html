<section style="max-width: 896px; margin: 0 auto;">
<div align="center">
<h1 style="color:#875A7B;">CRnD Web Diagram Plus</h1>
</div>
<div align="center">
<p><a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png" /></a> <a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://img.shields.io/badge/maintainer-CR&amp;D-purple.png" /></a> <a href="https://github.com/crnd-inc/crnd-web/tree/11.0/crnd_web_diagram_plus"><img alt="GitHub" src="https://img.shields.io/badge/GitHub-CRnD_Web_Diagram_Plus-green.png"/></a></p>
<p><br></p>
</div>
</section>

<section style="margin: 0 auto; max-width: 896px;">
<div>
<p>CR&amp;D fork of <a href="https://github.com/odoo/odoo/tree/11.0/addons/web_diagram" target="_blank">web_diagram</a> addon.</p>
<p>Changes:</p>
<ul>
    <li>Applied the following <a href="https://github.com/odoo/odoo/pull/18975">Pull Request</a> that fixes an error on the diagram view. It appears when the diagram object has 2 fields (one2many + many2one) that points to related nodes.</li>
    <li>Added possibility to specify background and foreground colors of nodes (via fields).</li>
    <li>Possible to store node position in database, thus there is no more need
        to rearrange flow manually each time you open diagram.</li>
</ul>
<p><br></p>
<p>An example of internal usage:</p>
<pre>
    &lt;record id="some_id" model="ir.ui.view"&gt;
        &lt;field name="model"&gt;model.name&lt;/field&gt;
        &lt;field name="type"&gt;diagram_plus&lt;/field&gt;
        &lt;field name="arch" type="xml"&gt;
            &lt;diagram_plus&gt;
                &lt;node object="name.of.model"
                      bgcolor="from_old_diagram_non_priority"
                      bg_color_field="name_of_field_bg_color"
                      fg_color_field="name_of_field_fg_color"
                      d_position_field="name_of_field_to_store_position"&gt;
                &lt;/node&gt;
                &lt;arrow object="name.of.model"
                       source="source_field(from)"
                       destination="destination_field(to)"
                       label="['name_of_label_field']"&gt;
                &lt;/arrow&gt;
            &lt;/diagram_plus&gt;
        &lt;/field&gt;
    &lt;/record&gt;
</pre>
<div align="center">
<img src="color_diagram.png" alt="colored diagram" style="max-width: 100%">
<p><br></p>
</div>
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color:#875A7B;">Launch your own ITSM system in 60 seconds:</h2>
<p>Create your own <a href="https://yodoo.systems/saas/template/">Bureaucrat ITSM</a> database
<a href="https://yodoo.systems"><img alt="yodoo.systems" src="https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png"/></a>
</p> 
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color: #138c33">Maintainer</h2>
<a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://crnd.pro/web/image/3699/300x140/crnd.png"/></a>
<div align="center">
<a class="btn btn-primary mb16" title="Website" href="https://crnd.pro"><i class="fa fa-globe"></i> Website</a>
<a class="btn btn-primary mb16" title="Contact us" href="mailto:<EMAIL>"><i class="fa fa-envelope"></i> Contact us</a>
<a class="btn btn-primary mb16" title="Bug" href="https://crnd.pro/requests"><i class="fa fa-bug"></i> Bug</a>
<a class="btn btn-primary mb16" title="Requests" href="https://crnd.pro/requests"><i class="fa fa-bullhorn"></i> Requests</a>
</div>
<div align="center">
<a class="btn btn-primary mb16" title="Blog" href="https://crnd.pro/blog"><i class="fa fa-quote-right"></i> Blog</a>
<a class="btn btn-primary mb16" title="Youtube Channel" href="https://www.youtube.com/channel/UCRSGFeR0NgqflPmzpDjL2tw"><i class="fa fa-youtube-play"></i> Youtube Channel</a>
<p><br></p>
</div>

<p>This module is maintained by the <a href="https://crnd.pro/">Center of Research &amp; Development</a>
company.
</p>
<p>
We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you. 
</p>
</div>
</section>
