<section style="max-width: 896px; margin: 0 auto;">
<div align="center">
<h1 style="color:#875A7B;">CRnD Web Tree Colored Field</h1>
</div>
<div align="center">
<p><a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png" /></a> <a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://img.shields.io/badge/maintainer-CR&amp;D-purple.png" /></a> <a href="https://github.com/crnd-inc/crnd-web/tree/11.0/crnd_web_diagram_fix"><img alt="GitHub" src="https://img.shields.io/badge/GitHub-CRnD_Web_Diagram_Fix-green.png"/></a></p>
<p><br></p>
</div>
</section>

<section style="margin: 0 auto; max-width: 896px;">
<div>
<p>CRnD Web Tree Colored Field widget provide to display tree cell with color from another field or color, that depends on value of other field.</p>
</div>
<p><br></p>
    <p>An example of internal usage defining color from another field:</p>
    <p>In Python code:</p>
    <pre>
        line_bg_color = fields.Char('rgba(R,G,B[,A])')
        line_label_color = fields.Char('rgba(R,G,B[,A])')
    </pre>
    <p>In XML code:</p>
    <pre>   &lt;record id="some_id" model="ir.ui.view"&gt;
        &lt;field name="model"&gt;model.name&lt;/field&gt;
        &lt;field name="arch" type="xml"&gt;
            &lt;tree&gt;
                &lt;field name="colored_field"
                          options="{
                              'field_bg_color': 'line_bg_color',
                              'field_label_color': 'line_label_color'}"/&gt;
                &lt;field name="line_bg_color" invisible="1"/&gt;
                &lt;field name="line_label_color" invisible="1"/&gt;
            &lt;/tree&gt;
        &lt;/field&gt;
    &lt;/record&gt;
    </pre>
    <p><br></p>
    <p>An example of internal usage defining color, depends on value of another field:</p>
    <p>In Python code:</p>
    <pre>
        state = fields.Selection([
        ('ok', 'Ok'),
        ('warning', 'Warning'),
        ('fail', 'Fail')
        ])
    </pre>
    <p>In XML code:</p>
    <pre>   &lt;record id="some_id" model="ir.ui.view"&gt;
        &lt;field name="model"&gt;model.name&lt;/field&gt;
        &lt;field name="arch" type="xml"&gt;
            &lt;tree&gt;
                &lt;field name="colored_field"
                          options="{&quot;field_label_color_expression&quot;: &quot;
                       red:state == 'fail';
                       #2F4F4F:state == 'warning';
                       green:state == 'ok'&quot;,
                                &quot;field_bg_color_expression&quot;: &quot;
                       white:state == fail;
                       yellow:state == 'warning';
                       #D3D3D3:state == 'ok'&quot;}"/>/&gt;
                &lt;field name="state" invisible="1"/&gt;
            &lt;/tree&gt;
        &lt;/field&gt;
    &lt;/record&gt;
    </pre>
</div>
</section>

<section style="max-width: 896px; margin: 0 auto;">
<div>
<h3>Bug Tracker</h3>
<p>Bugs are tracked on <a href="https://crnd.pro/requests">https://crnd.pro/requests</a>. In case of trouble, please report there.
</p>
<p><br></p>
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color:#875A7B;">Launch your own ITSM system in 60 seconds:</h2>
<p>Create your own <a href="https://yodoo.systems/saas/template/">Bureaucrat ITSM</a> database
<a href="https://yodoo.systems"><img alt="yodoo.systems" src="https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png"/></a>
</p>
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color: #138c33">Maintainer</h2>
<a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://crnd.pro/web/image/3699/300x140/crnd.png" /></a>
<p>Our web site: <a href="https://crnd.pro">https://crnd.pro</a>
</p>
<p>This module is maintained by the <a href="https://crnd.pro/">Center of Research &amp; Development</a>
company.
</p>
<p>
We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you.
</p>
<p>
For any questions <a href="mailto:<EMAIL>">contact us</a>.
</p>
</div>
</section>
