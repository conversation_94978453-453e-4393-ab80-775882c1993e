# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_tag_account
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 15:06+0000\n"
"PO-Revision-Date: 2023-07-26 09:29+0000\n"
"Last-Translator: Den_Sharaf <<EMAIL>>\n"
"Language-Team: Ukrainian <http://weblate.crnd.pro/projects/"
"generic-addons-15-0/generic_tag_account/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: generic_tag_account
#: model:ir.ui.menu,name:generic_tag_account.menu_account_tags
msgid "Account Tags"
msgstr "Теги бухобліку"

#. module: generic_tag_account
#: model:ir.model.fields,help:generic_tag_account.field_account_move__search_tag_id
msgid "Find all records that contain this tag"
msgstr "Знайти всі записи які мають цей тег"

#. module: generic_tag_account
#: model:ir.model.fields,help:generic_tag_account.field_account_move__search_no_tag_id
msgid "Find all records that have no this tag"
msgstr "Знайти всі записи які не мають цей тег"

#. module: generic_tag_account
#: model:ir.actions.act_window,name:generic_tag_account.account_move_tags_view_action
#: model:ir.ui.menu,name:generic_tag_account.menu_account_move_tags_id
msgid "Invoice Tags"
msgstr "Теги реалізації"

#. module: generic_tag_account
#: model:generic.tag.model,name:generic_tag_account.generic_tag_model_account_move
#: model:ir.model,name:generic_tag_account.model_account_move
msgid "Journal Entry"
msgstr "Запис в журналі"

#. module: generic_tag_account
#: model:ir.model.fields,field_description:generic_tag_account.field_account_move__search_no_tag_id
msgid "No tag"
msgstr "Немає тегів"

#. module: generic_tag_account
#: model:ir.model.fields,field_description:generic_tag_account.field_account_move__search_tag_id
msgid "Tag"
msgstr "Тег"

#. module: generic_tag_account
#: model:ir.model.fields,field_description:generic_tag_account.field_account_move__tag_ids
msgid "Tags"
msgstr "Теги"

#. module: generic_tag_account
#: model_terms:ir.ui.view,arch_db:generic_tag_account.account_invoice_tree_view
#: model_terms:ir.ui.view,arch_db:generic_tag_account.view_account_move_tag_form
msgid "Tags..."
msgstr "Теги..."

#~ msgid "Invoice"
#~ msgstr "Реалізація"
