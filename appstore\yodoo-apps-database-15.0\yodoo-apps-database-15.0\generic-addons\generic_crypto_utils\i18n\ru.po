# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_crypto_utils
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-20 12:35+0000\n"
"PO-Revision-Date: 2020-10-20 12:35+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_crypto_utils
#: model:ir.model,name:generic_crypto_utils.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__create_uid
msgid "Created by"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__create_date
msgid "Created on"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__display_name
msgid "Display Name"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model,name:generic_crypto_utils.model_generic_crypto_param
msgid "Generic Crypto Param"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__id
msgid "ID"
msgstr ""

#. module: generic_crypto_utils
#: code:addons/generic_crypto_utils/models/generic_crypto_param.py:0
#, python-format
msgid ""
"Invalid 'crypto_token'! Ensure it is valid 32-bytes base64-encoded string!"
msgstr ""

#. module: generic_crypto_utils
#: code:addons/generic_crypto_utils/models/generic_crypto_param.py:0
#, python-format
msgid ""
"It seems that python package 'cryptography' is not installed!Please, install "
"[cryptography](https://pypi.org/project/cryptography/) package and try again"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__key
msgid "Key"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param____last_update
msgid "Last Modified on"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__write_uid
msgid "Last Updated by"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__write_date
msgid "Last Updated on"
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.constraint,message:generic_crypto_utils.constraint_generic_crypto_param_key_uniq
msgid "Param key must be unique."
msgstr ""

#. module: generic_crypto_utils
#: model:ir.model.fields,field_description:generic_crypto_utils.field_generic_crypto_param__value
msgid "Value"
msgstr ""

#. module: generic_crypto_utils
#: code:addons/generic_crypto_utils/models/generic_crypto_param.py:0
#, python-format
msgid ""
"You must add 'crypto_token' to 'odoo.conf' to be able to use this feature"
msgstr ""
