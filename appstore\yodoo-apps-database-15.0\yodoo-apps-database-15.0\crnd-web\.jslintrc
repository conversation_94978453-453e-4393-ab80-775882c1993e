{"globals": {"$": false, "_": false, "fuzzy": false, "jQuery": false, "moment": false, "odoo": false, "openerp": false, "self": false}, "env": {"browser": true, "es8": true}, "rules": {"no-alert": "error", "no-array-constructor": "error", "no-bitwise": "off", "no-caller": "error", "no-case-declarations": "error", "no-catch-shadow": "error", "no-class-assign": "error", "no-cond-assign": "error", "no-confusing-arrow": "error", "no-console": "off", "no-const-assign": "error", "no-constant-condition": "error", "no-continue": "off", "no-control-regex": "error", "no-debugger": "error", "no-delete-var": "error", "no-div-regex": "error", "no-dupe-args": "error", "no-dupe-class-members": "error", "no-dupe-keys": "error", "no-duplicate-case": "error", "no-duplicate-imports": "error", "no-else-return": "error", "no-empty": "error", "no-empty-character-class": "error", "no-empty-function": "error", "no-empty-pattern": "error", "no-eq-null": "error", "no-eval": "error", "no-ex-assign": "error", "no-extend-native": "error", "no-extra-bind": "error", "no-extra-boolean-cast": "error", "no-extra-label": "error", "no-extra-parens": "error", "no-extra-semi": "error", "no-fallthrough": "error", "no-floating-decimal": "error", "no-func-assign": "error", "no-implicit-coercion": ["error", {"allow": ["~"]}], "no-implicit-globals": "error", "no-implied-eval": "error", "no-inline-comments": "error", "no-inner-declarations": "error", "no-invalid-regexp": "error", "no-invalid-this": "off", "no-irregular-whitespace": "error", "no-iterator": "error", "no-label-var": "error", "no-labels": "error", "no-lone-blocks": "error", "no-lonely-if": "error", "no-loop-func": "off", "no-magic-numbers": "off", "no-mixed-operators": "error", "no-mixed-requires": "error", "no-mixed-spaces-and-tabs": "error", "no-multi-spaces": "error", "no-multi-str": "error", "no-multiple-empty-lines": "error", "no-native-reassign": "error", "no-negated-condition": "error", "no-negated-in-lhs": "error", "no-nested-ternary": "off", "no-new": "error", "no-new-func": "error", "no-new-object": "error", "no-new-require": "error", "no-new-symbol": "error", "no-new-wrappers": "error", "no-obj-calls": "error", "no-octal": "error", "no-octal-escape": "error", "no-param-reassign": "error", "no-path-concat": "error", "no-plusplus": "off", "no-process-env": "error", "no-process-exit": "error", "no-proto": "error", "no-prototype-builtins": "error", "no-redeclare": "error", "no-regex-spaces": "error", "no-restricted-globals": "error", "no-restricted-imports": "error", "no-restricted-modules": "error", "no-restricted-syntax": "error", "no-return-assign": "error", "no-script-url": "error", "no-self-assign": "error", "no-self-compare": "error", "no-sequences": "error", "no-shadow": "error", "no-shadow-restricted-names": "error", "no-whitespace-before-property": "error", "no-spaced-func": "error", "no-sparse-arrays": "error", "no-sync": "error", "no-tabs": "error", "no-ternary": "off", "no-trailing-spaces": "error", "no-this-before-super": "error", "no-throw-literal": "error", "no-undef": "error", "no-undef-init": "error", "no-undefined": "off", "no-unexpected-multiline": "error", "no-underscore-dangle": "off", "no-unmodified-loop-condition": "error", "no-unneeded-ternary": "error", "no-unreachable": "error", "no-unsafe-finally": "error", "no-unused-expressions": "error", "no-unused-labels": "error", "no-unused-vars": "error", "no-use-before-define": "error", "no-useless-call": "error", "no-useless-computed-key": "error", "no-useless-concat": "error", "no-useless-constructor": "error", "no-useless-escape": "error", "no-useless-rename": "error", "no-void": "error", "no-var": "off", "no-warning-comments": "off", "no-with": "error", "array-bracket-spacing": "off", "array-callback-return": "error", "arrow-body-style": "error", "arrow-parens": "error", "arrow-spacing": "off", "accessor-pairs": "error", "block-scoped-var": "off", "block-spacing": ["error", "always"], "brace-style": "error", "callback-return": "error", "camelcase": "off", "capitalized-comments": ["error", "always", {"ignoreConsecutiveComments": true, "ignoreInlineComments": true}], "comma-dangle": ["error", "always-multiline"], "comma-spacing": ["error", {"before": false, "after": true}], "comma-style": "error", "complexity": ["error", 15], "computed-property-spacing": "off", "consistent-return": "off", "consistent-this": "off", "constructor-super": "error", "curly": "error", "default-case": "off", "dot-location": ["error", "property"], "dot-notation": "error", "eol-last": "error", "eqeqeq": "error", "func-names": "off", "func-style": "off", "generator-star-spacing": "off", "global-require": "error", "guard-for-in": "off", "handle-callback-err": "error", "id-blacklist": "error", "id-length": "off", "id-match": "error", "indent": "error", "init-declarations": "error", "jsx-quotes": "error", "key-spacing": "off", "keyword-spacing": "error", "linebreak-style": ["error", "unix"], "lines-around-comment": "error", "max-depth": "error", "max-len": ["error", {"code": 80, "ignorePattern": "odoo\\.define\\(", "tabWidth": 4}], "max-lines": "off", "max-nested-callbacks": "error", "max-params": "off", "max-statements": "off", "max-statements-per-line": "error", "multiline-ternary": "off", "new-cap": "off", "new-parens": "error", "newline-after-var": "off", "newline-before-return": "off", "newline-per-chained-call": "off", "object-curly-newline": ["error", {"consistent": true}], "object-curly-spacing": ["error", "never"], "object-property-newline": ["error", {"allowAllPropertiesOnSameLine": true}], "object-shorthand": "off", "one-var": "off", "one-var-declaration-per-line": "off", "operator-assignment": "error", "operator-linebreak": "error", "padded-blocks": "off", "prefer-arrow-callback": "off", "prefer-const": "error", "prefer-reflect": "off", "prefer-rest-params": "off", "prefer-spread": "off", "prefer-template": "off", "quote-props": "off", "quotes": "off", "radix": "error", "require-yield": "error", "rest-spread-spacing": "off", "semi": ["error", "always"], "semi-spacing": "error", "sort-imports": "error", "sort-vars": "off", "space-before-blocks": "error", "space-before-function-paren": "error", "space-in-parens": "off", "space-infix-ops": "off", "space-unary-ops": "off", "spaced-comment": ["error", "always"], "strict": ["error", "function"], "template-curly-spacing": "off", "unicode-bom": "error", "use-isnan": "error", "valid-jsdoc": ["error", {"prefer": {"arg": "param", "argument": "param", "augments": "extends", "constructor": "class", "exception": "throws", "func": "function", "method": "function", "prop": "property", "return": "returns", "virtual": "abstract", "yield": "yields"}, "preferType": {"array": "Array", "bool": "Boolean", "boolean": "Boolean", "number": "Number", "object": "Object", "str": "String", "string": "String"}, "requireParamDescription": false, "requireReturn": false, "requireReturnDescription": false, "requireReturnType": false}], "valid-typeof": "error", "vars-on-top": "off", "wrap-iife": "error", "wrap-regex": "error", "yield-star-spacing": "off", "yoda": "error"}, "parserOptions": {}}