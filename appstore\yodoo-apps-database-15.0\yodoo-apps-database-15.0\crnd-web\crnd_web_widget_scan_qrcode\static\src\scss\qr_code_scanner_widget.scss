.qr_code_scanner_widget {

  .qr_code_widget_bottom_block {

  }
}

.qr_code_scanner_popup {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .qr_code_scanner_wrapper {
    position: relative;
    width: 600px;

    @include media-breakpoint-down(md) {
        width: 100%;
    }

    .qr_code_scanner_container {
      background-color: white;
    }

    .qr_code_scanner_success_message {
      width: 100%;
      min-height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: white;

      span {
        font-size: 1.3rem;
      }

      .btn_block {
        margin-top: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;

        button {
          min-width: 9rem;
          font-size: 1.2rem !important;

          &:first-child {
            margin-right: 0.5rem;
          }
        }
      }
    }
  }

  .qr_code_scanner_close_popup_btn {
    position: absolute;
    right: 2rem;
    top: 2rem;
    font-size: 4rem;
    color: white;
    cursor: pointer;
  }
}
