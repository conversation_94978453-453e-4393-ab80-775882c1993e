Generic Location
================

.. |badge1| image:: https://img.shields.io/badge/pipeline-pass-brightgreen.png
    :target: https://github.com/crnd-inc/generic-addons

.. |badge2| image:: https://img.shields.io/badge/license-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3

.. |badge3| image:: https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png
    :target: https://yodoo.systems
    
.. |badge5| image:: https://img.shields.io/badge/maintainer-CR&D-purple.png
    :target: https://crnd.pro/
    
.. |badge4| image:: https://img.shields.io/badge/docs-Generic_Location-yellowgreen.png
    :target: https://crnd.pro/doc-bureaucrat-itsm/11.0/en/Generic_Location_admin_eng

.. |badge6| image:: https://img.shields.io/badge/GitHub-Generic_Location-green.png
    :target: https://github.com/crnd-inc/generic-addons/tree/11.0/generic_location


|badge1| |badge2| |badge4| |badge5| |badge6|

Generic Location is a module developed by the `Center of Research &
Development company <https://crnd.pro/>`__.

It allows you to make an abstract description of the objects location
relative to the general location (for example: house3 ▶️ office5 ▶️
room2 ▶️ table5).

Each sub-location may have many own sub-locations, so that you can
easily describe complex objects using a tree-like structure.

The module is designed for use with other Odoo applications and modules
and can extend its functionality depending on the installed modules.

**Location access feature:** if you grant access for the user to some location, this user will automatically have access to all of its sub-locations.

Main Features of the Generic Location Module:
'''''''''''''''''''''''''''''''''''''''''''''

-  *Create and customize an abstract location model of your business
   objects.*
-  *Link your resources, services, etc. to crated locations.*
-  *Keep an eye on your resources, services etc. for more convenient
   work.*

Read the `Generic Location Module Guide <https://crnd.pro/doc-bureaucrat-itsm/11.0/en/Generic_Location_admin_eng/>`__ for more information.


This module is part of the Bureaucrat ITSM project. 
You can try it by the references below.

Launch your own ITSM system in 60 seconds:
''''''''''''''''''''''''''''''''''''''''''

Create your own `Bureaucrat ITSM <https://yodoo.systems/saas/template/bureaucrat-itsm-demo-data-95>`__ database

|badge3| 

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/crnd-inc/generic-addons/issues>`_.
In case of trouble, please check there if your issue has already been reported.


Maintainer
''''''''''
.. image:: https://crnd.pro/web/image/3699/300x140/crnd.png

Our web site: https://crnd.pro/

This module is maintained by the Center of Research & Development company.

We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you. 

For any questions `contact us <mailto:<EMAIL>>`__.

