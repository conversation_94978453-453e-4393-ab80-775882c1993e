<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_generic_tag_model_search" model="ir.ui.view">
        <field name="name">generic.tag.model.search</field>
        <field name="model">generic.tag.model</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="model"/>
                <field name="res_model_id"/>
            </search>
        </field>
    </record>

    <record id="view_generic_tag_model_tree" model="ir.ui.view">
        <field name="name">generic.tag.model.tree</field>
        <field name="model">generic.tag.model</field>
        <field name="arch" type="xml">
            <tree create="false" delete="false">
                <field name="name"/>
                <field name="model"/>
                <field name="res_model_id"/>
                <field name="tags_count"/>
                <button name="action_show_tags" type="object" string="Show Tags" icon="fa-search"/>
            </tree>
        </field>
    </record>

    <record id="view_generic_tag_model_form" model="ir.ui.view">
        <field name="name">generic.tag.model.form</field>
        <field name="model">generic.tag.model</field>
        <field name="arch" type="xml">
            <form create="false" delete="false">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_show_tags"
                                type="object" icon="fa-bars"
                                class="oe_stat_button">
                            <field name="tags_count" string="Tags" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <field name="res_model_id"
                               options="{'no_create': True, 'no_create_edit': True}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_generic_tag_models_view" model="ir.actions.act_window">
        <field name="name">Models</field>
        <field name="res_model">generic.tag.model</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_generic_tag_model_tree"/>
    </record>

    <menuitem action="action_generic_tag_models_view" id="menu_action_generic_tag_modeles_view"
              parent="menu_generic_tags_configuration_root" sequence="30"/>
</odoo>
