# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_condition
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 15:09+0000\n"
"PO-Revision-Date: 2019-09-13 15:09+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"<b>Warning:</b> This feature is deprecated,\n"
"                                because filters cannot handle NewId records "
"that used\n"
"                                during onchange computation."
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "<b>Warning:</b> This feature is experimental."
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition_domain_leaf__type__operator-and
#, python-format
msgid "AND"
msgstr "И"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_absolute
msgid "Absolute"
msgstr "По модулю"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Accounting date"
msgstr "Дата учета"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__active
msgid "Active"
msgstr "Активно"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_find_order_by_direction__asc
msgid "Ascending"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Based on"
msgstr "Основан на"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__model_id
msgid "Based on model"
msgstr "Основан на модели"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"By this type you can create the condition to compare monetary indicators. "
"<br/>\n"
"                                <br/>"
msgstr ""
"Используя этот тип, вы можете создать условие для сравнения денежных "
"показателей. <br/>\n"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"By this type you can describe the condition as an expression in the Python "
"programming language. <br/>\n"
"                                <br/>"
msgstr ""
"Используя этот тип, вы можете описать условие как выражение на языке "
"программирования Python.  <br/>\n"
"                                <br/>"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Cannot apply conditions %(cond_name)s [%(cond_id)s] of type 'Filter' to "
"NewId object"
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid "Cannot check conditions for empty recordset"
msgstr "Невозможно проверить условия для пустой выборки данных"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_string_operator_icase
msgid "Case insensitive"
msgstr "Независимо от регистра"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Check"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__check_field_id
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Check Field"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_user_check_type
msgid "Check Type"
msgstr "Тип проверки"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_related_field_field_id
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_field_id
msgid "Check field"
msgstr "Проверить поле"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_condition_ids
msgid "Check set of other conditions"
msgstr "Проверить набор других условий"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Checking condition %(cond)s on document %(doc)s caused error. Notify "
"administrator to fix it.\n"
"\n"
"---\n"
"%(error_msg)s"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_user_check_type__checks
msgid "Checks"
msgstr "Проверки"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Checks: Check if current user satisfies specified conditions"
msgstr "Проверить выполняются ли выбранные условия для текущего пользователя"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_date_diff_uom
msgid "Choose Unit of Measurement for date diff here"
msgstr "Выберите единицы измерения для разницы дат"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__model_id
msgid "Choose model to apply condition to"
msgstr "Выберите можель, для которой применить условия"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_rel_record_operator
msgid ""
"Choose way related record will be checked:\n"
"- Match: return True if all filtered records match condition.\n"
"- Contains: return True if at least one of filtered records match 'check' "
"conditions"
msgstr ""
"Выберите способ проверки связанных запросов:\n"
"- Совпадение: Истинно, если все выбранные записи соответствуют условиям "
"проверки.\n"
"- Включение: Истинно, если хоть одна из выбранных записей соответствует "
"условиям проверки."

#. module: generic_condition
#: model:ir.filters,name:generic_condition.demo_filter_partner_city_kyiv
msgid "City: Kyiv"
msgstr "Город: Киев"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__color
msgid "Color"
msgstr "Цвет"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__condition_id
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__condition_id
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition_domain_leaf__type__search-condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Condition"
msgstr "Условие"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_condition_ids
msgid "Condition (condition group)"
msgstr "Условие (группа условий)"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_condition_ids_operator
msgid "Condition (condition group): operator"
msgstr "Условие (группа условий): оператор"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_condition_id
msgid "Condition (condition)"
msgstr "Условие (условие)"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_eval
msgid "Condition (eval)"
msgstr "Условие (python-выражение)"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_filter_id
msgid "Condition (filter)"
msgstr "Условие (фильтр)"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_test_wizard_form
msgid "Condition Debug Log"
msgstr "Журнал отладки условия"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_check_condition_ids
msgid "Condition Find Check Condition"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_fetch_type
msgid "Condition Find Fetch Type"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_if_not_found
msgid "Condition Find If Not Found"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_order_by_direction
msgid "Condition Find Order By Direction"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_order_by_field_id
msgid "Condition Find Order By Field"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_search_domain_ids
msgid "Condition Find Search Domain"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_search_model_id
msgid "Condition Find Search Model"
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid "Condition Find: model if order by field must be same as search model"
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_test_wizard_form
msgid "Condition Result"
msgstr "Результат проверки условия"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__condition_group
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Condition group"
msgstr "Группа условий"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Condition search"
msgstr "Поиск условий"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_user_checks_condition_ids
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Conditions"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__sequence
msgid "Conditions with smaller value in this field will be checked first"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_rel_record_operator__contains
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_related_field_operator__contains
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_string_operator__contains
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_string_operator_html__contains
msgid "Contains"
msgstr "Включает"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_parenter_country_is_ukraine
msgid "Country: Ukraine"
msgstr "Страна: Украина"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__create_uid
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__create_uid
msgid "Created by"
msgstr "Создал"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__create_date
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__create_date
msgid "Created on"
msgstr "Создано"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_currency_field_id
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_value_currency_id
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Currency"
msgstr "Валюта"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_monetary_curency_date_type__now
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
#, python-format
msgid "Current date"
msgstr "Текущая дата"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__current_user
msgid "Current user"
msgstr "Текущий пользователь"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition_domain_leaf.py:0
#, python-format
msgid ""
"Currently it is not supported to check fields of %(field_type)s type with "
"static value"
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_curency_date_date
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_monetary_curency_date_type__date
#, python-format
msgid "Date"
msgstr "Дата"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_uom
msgid "Date diff UoM"
msgstr "Разница дат: ЕИ"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_operator
msgid "Date diff operator"
msgstr "Разница дат: оператор"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_value
msgid "Date diff value"
msgstr "Разница дат: значение"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__date_diff
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Date difference"
msgstr "Разница дат"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_end_date
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_end_datetime
msgid "Date end"
msgstr "Дата завершения"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_end_field
msgid "Date end field"
msgstr "Дата завершения: поле"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_end_type
msgid "Date end type"
msgstr "Дата завершения: тип"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_start_date
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_start_datetime
msgid "Date start"
msgstr "Дата начала"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_start_field
msgid "Date start field"
msgstr "Дата начала: поле"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_date_diff_date_start_type
msgid "Date start type"
msgstr "Дата начала: тип"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid "Datetime"
msgstr "Дата-время"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_date_diff_uom__days
msgid "Days"
msgstr "Дни"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__debug_log
msgid "Debug Log"
msgstr "Журнал отладки"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_find_order_by_direction__desc
msgid "Descending"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__description
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Description"
msgstr "Описание"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Description..."
msgstr "Описание..."

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__display_name
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__display_name
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__display_name
msgid "Display Name"
msgstr "Название для отображения"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__enable_caching
msgid "Enable Caching"
msgstr "Включить кеширование"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "End Date"
msgstr "Дата завершения"

#. module: generic_condition
#: code:addons/generic_condition/wizard/test_condition.py:0
#, python-format
msgid "Error"
msgstr "Ошибка"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_find_if_not_found__false
msgid "Evaluate to False"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_find_if_not_found__true
msgid "Evaluate to True"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__eval
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Expression"
msgstr "Выражение"

#. module: generic_condition
#: code:addons/generic_condition/tests/test_condition.py:0
#: code:addons/generic_condition/wizard/test_condition.py:0
#, python-format
msgid "Fail"
msgstr "Провал"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_value_boolean__false
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition_domain_leaf__value_boolean__false
msgid "False"
msgstr "Фальш"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_curency_date_field_id
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_field_id
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_monetary_curency_date_type__field
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_user_check_type__field
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
#, python-format
msgid "Field"
msgstr "Поле"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__check_field_type
msgid "Field Type"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_user_user_field_id
msgid "Field in object being checked, that points to user."
msgstr "Проверяется поле объекта, которое указывает на пользователя."

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_type
msgid "Field type"
msgstr "Тип поля"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_monetary_currency_field_id
msgid "Field with currency for field being checked"
msgstr "Поле, содержащее валюту, для поля, которое проверяется"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Field: Check if current user is present in field of object"
msgstr "Проверить есть ли текущий пользователь в поле обЪекта"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__filter
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Filter"
msgstr "Фильтр"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Filter records"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__find
msgid "Find & Check"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_find_fetch_type__first
msgid "First"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition_domain_leaf__check_field_relation
msgid "For relationship fields, the technical name of the target model"
msgstr ""

#. module: generic_condition
#: model:ir.model,name:generic_condition.model_generic_condition
msgid "Generic Condition"
msgstr "Универсальное условие"

#. module: generic_condition
#: model:res.groups,comment:generic_condition.group_generic_condition_manage
#: model:res.groups,name:generic_condition.group_generic_condition_manage
msgid "Generic Condition Manage"
msgstr ""

#. module: generic_condition
#: model:ir.model,name:generic_condition.model_generic_condition_domain_leaf
msgid "Generic Condition: Domain Leaf"
msgstr ""

#. module: generic_condition
#: model:ir.actions.act_window,name:generic_condition.generic_condition_action
#: model:ir.ui.menu,name:generic_condition.menu_generic_condition
msgid "Generic Conditions"
msgstr "Универсальные условия"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Generic conditions misconfigured!\n"
"object's model and condition's model does not match:\n"
"\tcondition: %(condition)s [%(condition_id)d]\tobject: %(object)s "
"[%(object_id)d]\tobject's model: %(object_model)s\n"
"\tcondition's model: %(condition_model)s\n"
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Group by..."
msgstr "Группировать по…"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__has_message
msgid "Has Message"
msgstr ""

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_has_contact_green
msgid "Has contact 'Green'"
msgstr "Содержит контакт 'Green'"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_has_only_contacts
msgid "Has only contacts"
msgstr "Содержит только контакты"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"Here you can create a condition that relates to the simple field of the Odoo "
"object. <br/>\n"
"                                <br/>"
msgstr ""
"Создайте условие, которое связано с простым полем объекта Оду. <br/>\n"
"                                <br/>"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"Here you can specify the filter for verification. <br/>\n"
"                                <br/>"
msgstr ""
"Укажите фильтр. <br/>\n"
"                                <br/>"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_date_diff_uom__hours
msgid "Hours"
msgstr "Часы"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__id
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__id
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__id
msgid "ID"
msgstr "ID"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition_test_condition__res_id
msgid "ID of object to test condition on"
msgstr "ID объекта, на котором протестировать условие"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__message_needaction
#: model:ir.model.fields,help:generic_condition.field_generic_condition__message_unread
msgid "If checked, new messages require your attention."
msgstr "Если отмечено - новые сообщения требуют Вашего внимания."

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Если выбрано, значит некоторые сообщение имеют ошибку доставки"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_date_diff_absolute
msgid ""
"If checked, then absolute date difference will be checked. (date difference "
"will be positive always)"
msgstr ""
"Если выбрано, разница дат будет рассчитываться по модулю (разница дат всегда "
"будет положительной)"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "If records not found"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__enable_caching
msgid ""
"If set, then condition result for a specific object will be cached during "
"one condition chain call. This may speed up condition processing."
msgstr ""
"Если выбрано, результат проверки для каждого отдельного объекта будет "
"кешироваться во время проверки одной цепи условий. Это может ускорить "
"обработку условий."

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"In that case you can select one of the already created conditions (this "
"could be useful if you use the same condition but with inversion or as a "
"superuser). <br/>\n"
"                                <br/>"
msgstr ""
"В этом случае вы можете выбрать одно из уже созданных условий (это может "
"быть полезно при использовании такого же условия, но с инверсией, или как "
"суперпользователь). <br/>\n"
"                                <br/>"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Incorrect Condition (condition group) selected!\n"
"Base condition: %(base_cond)s [%(base_cond_id)s]\n"
"Condition with wrong model: %(cond)s [%(cond_id)s]"
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Incorrect Conditon field set for condition: %(condition)s [%(condition_id)s]"
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Incorrect Filter field set for condition: %(condition)s [%(condition_id)s]"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__invert
msgid "Invert (Not)"
msgstr "Инвертировать (Не)"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__invert
msgid "Invert condition result."
msgstr "Инвертировать результат условия"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition____last_update
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf____last_update
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__write_uid
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__write_date
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_condition_id
msgid "Link to another condition. Usualy used to get inversed condition"
msgstr ""
"Связать с другим условием. Обычно используется для получения "
"противоположного условия"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_rel_record_operator__match
msgid "Match"
msgstr "Совпадение"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_date_diff_uom__minutes
msgid "Minutes"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__based_on
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_find_search_model_name
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Model"
msgstr "Модель"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Model (%(cmodel)s) of check condition (%(cond)s) must match Search model "
"(%(smodel)s)!"
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Model Name"
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Model of check field (%(dmodel)s) of domain leaf (%(domain)s) must match "
"Search model (%(smodel)s)!"
msgstr ""

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid ""
"Model of value field (%(dfield)s) of domain leaf (%(domain)s) must match "
"Condition model (%(smodel)s)!"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__monetary_field
msgid "Monetary field"
msgstr "Денежное поле"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_date_diff_uom__months
msgid "Months"
msgstr "Месяцев"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__name
msgid "Name"
msgstr "Название"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_related_field_operator__not_set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_date_operator__not_set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_selection_operator__not_set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_string_operator__not_set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_string_operator_html__not_set
msgid "Not set"
msgstr "Не установлено"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих внимания"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Кол-во сообщений с ошибкой доставки"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition_domain_leaf__type__operator-or
#, python-format
msgid "OR"
msgstr "Или"

#. module: generic_condition
#: code:addons/generic_condition/wizard/test_condition.py:0
#, python-format
msgid "Object (model: %(model)s; id: %(obj_id)s) not found"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition_domain_leaf__value_type__object-field
msgid "Object Field"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition_domain_leaf__value_type
msgid ""
"Object Field: uses value from field of the object being checked.Static "
"Value: static value to check."
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__res_id
msgid "Object ID"
msgstr "ID объекта"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__res_model
msgid "Object Model"
msgstr "Модель объекта"

#. module: generic_condition
#: code:addons/generic_condition/tests/test_condition.py:0
#: code:addons/generic_condition/wizard/test_condition.py:0
#, python-format
msgid "Ok"
msgstr "Ок"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_user_check_type__one_of
msgid "One of"
msgstr "Один из"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "One of: Check if current user is present in specified list of users"
msgstr ""
"Проверить находится ли текущий пользователь в указанном списке пользователей"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_operator
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_related_field_operator
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_date_operator
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_number_operator
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_selection_operator
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_string_operator
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_string_operator_html
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Operator"
msgstr "Оператор"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Order By Direction"
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Order By Field"
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Please, change this condition to use other types."
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_eval
msgid "Python expression. 'obj' are present in context."
msgstr "Python-выражение. Переменная 'obj' присутствует в контексте."

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_string_operator_regex
msgid "Regular expression"
msgstr "Регулярное выражение"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_rel_field_id
msgid "Related Field"
msgstr "Связанное поле"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_related_field_model
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__check_field_relation
msgid "Related Model"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_rel_conditions
msgid "Related check conditions"
msgstr "Связанная модель"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_rel_conditions_operator
msgid "Related check conditions operator"
msgstr "Связанные условия проверки: оператор"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__related_conditions
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Related conditions"
msgstr "Связанные условия"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__related_field
msgid "Related field"
msgstr "Связанное поле"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_rel_field_id_model_id
msgid "Related field: model"
msgstr "Связанное поле: модель"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_rel_filter_conditions
msgid "Related filter conditions"
msgstr "Условия фильтра связанных записей"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_rel_filter_conditions_operator
msgid "Related filter conditions operator"
msgstr "Условия фильтра связанных записей: оператор"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_rel_record_operator
msgid "Related record operator"
msgstr "Связанные записи: оператор"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__result
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Result"
msgstr "Результат"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__with_sudo
msgid "Run this condition as superuser."
msgstr "Запустить это условие как суперпользователь"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Search"
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Search condition"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__sequence
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_related_field_operator__set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_date_operator__set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_selection_operator__set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_string_operator__set
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_string_operator_html__set
msgid "Set"
msgstr "Установлено"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__type__simple_field
msgid "Simple field"
msgstr "Простое поле"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Start Date"
msgstr "Дата начала"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition_domain_leaf__value_type__static-value
msgid "Static Value"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_related_field_model
msgid "Technical name of related field's model"
msgstr "Техническое название модели связанного поля"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_test_wizard_form
msgid "Test"
msgstr "Проверка"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_test_condition__test_as_user_id
msgid "Test As User"
msgstr "Проверить как пользователь"

#. module: generic_condition
#: model:ir.actions.act_window,name:generic_condition.action_generic_condition_test_wizard_view
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_test_wizard_form
msgid "Test Condition"
msgstr "Проверить условие"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "The following variables are available:"
msgstr "Доступны слудующие переменные:"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "There following check types"
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"This type of condition applies to related fields. <br/>\n"
"                                <br/>"
msgstr ""
"Этот тип условия применяется для связанных полей. <br/>\n"
"                                <br/>"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"This type of condition can be used to find related objects in arbitary "
"table\n"
"                                and check them."
msgstr ""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"This type of condition is used to check the properties of related objects. "
"<br/>\n"
"                                <br/>"
msgstr ""
"Этот тип условия используется для проверки связанных объектов. <br/>\n"
"                                <br/>"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"This type of condition refers to the current user and verifies whether the "
"current user satisfies conditions."
msgstr ""
"Этот тип условия относится<br/> к текущему пользователю и проверяет<br/> "
"выполняются ли для него выбранные условия."

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"This type of the condition allows you to estimate the date difference "
"between certain events. <br/>\n"
"                                <br/>"
msgstr ""
"Этот тип условий позволяет оценить разницу дат между определенными "
"событиями. <br/>\n"
"                                <br/>"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_simple_field_value_boolean__true
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition_domain_leaf__value_boolean__true
msgid "True"
msgstr "Истина"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_curency_date_type
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__type
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__type
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "Type"
msgstr "Тип"

#. module: generic_condition
#: code:addons/generic_condition/wizard/test_condition.py:0
#, python-format
msgid "Unknown"
msgstr "Неизвестно"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные Сообщения"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_rel_filter_conditions
msgid ""
"Used together with Related Field. These conditions are used to filter "
"related items that will be checked by 'Related check conditions'. If this "
"conditions evaluates to False for some object, that this object will not be "
"checked"
msgstr ""
"Используется для проверки связанного поля. Эти условия используются, чтобы "
"отфильтровать связанные элементы, которые будут проверены условиями "
"'Связанные условия проверки'. Если эти условия будут пересчитаны в False для "
"какого-нибудь объекта, этот объект не будет проверен"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_rel_conditions
msgid ""
"Used together with Related Field. These conditions will be used to check "
"objects that passed filter conditions. And result of these related "
"conditions will be used as result"
msgstr ""
"Используются для проверки связанного поля. Эти условия будут использованы "
"для проверки объектов, которые были отфильтрованы 'Условиями фильтра "
"связанных записей'. Результат проверки данных условий будет использован как "
"результат проверки этого условия"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_user_user_field_id
msgid "User Field"
msgstr "Поле пользователя"

#. module: generic_condition
#: model:ir.model.fields,help:generic_condition.field_generic_condition__condition_filter_id
msgid "User filter to be applied by this condition."
msgstr "Фильтр пользователя, который будет применен данным условием."

#. module: generic_condition
#: model:generic.condition,name:generic_condition.condition_user_is_external
msgid "User is external"
msgstr "Внешний пользователь"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.condition_user_is_internal
msgid "User is internal"
msgstr "Внутренний пользователь"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_user_one_of_user_ids
msgid "Users"
msgstr "Пользователи"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_monetary_value
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_related_field_value_id
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_value_boolean
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_value_char
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_value_date
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_value_datetime
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_value_float
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_value_integer
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__condition_simple_field_value_selection
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "Value"
msgstr "Значение"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_boolean
msgid "Value Boolean"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_char
msgid "Value Char"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_display
msgid "Value Display"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_field_id
msgid "Value Field"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_field_operator
msgid "Value Field Operator"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_float
msgid "Value Float"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_integer
msgid "Value Integer"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_res_id
msgid "Value Res"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_selection
msgid "Value Selection"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition_domain_leaf__value_type
msgid "Value Type"
msgstr ""

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_date_diff_uom__weeks
msgid "Weeks"
msgstr "Недели"

#. module: generic_condition
#: model:ir.model.fields,field_description:generic_condition.field_generic_condition__with_sudo
msgid "With Sudo"
msgstr "С правами суперпользователя"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_search_view
msgid "With sudo"
msgstr "С правами суперпользователя"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid ""
"With this type of condition you can use multiple conditions simultaneously. "
"<br/>\n"
"                                <br/>"
msgstr ""
"С помощью этого типа условия вы можете использовать несколько условий "
"одновременно. <br/>\n"
"                                <br/>"

#. module: generic_condition
#: model:ir.model,name:generic_condition.model_generic_condition_test_condition
msgid "Wizard: Test generic condition"
msgstr "Мастер: Протестировать универсальное условие"

#. module: generic_condition
#: code:addons/generic_condition/models/generic_condition.py:0
#, python-format
msgid "Wrong Related Field / Based on combination"
msgstr "Некорректное сочитание: 'Связанного поля' и 'Основан на'"

#. module: generic_condition
#: model:ir.model.fields.selection,name:generic_condition.selection__generic_condition__condition_date_diff_uom__years
msgid "Years"
msgstr "Годы"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_test_wizard_form
msgid "_Cancel"
msgstr "_Cancel"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_city_kyiv
#: model:generic.condition,name:generic_condition.demo_condition_partner_not_sx_corp_but_kyiv
msgid "city: Kyiv"
msgstr "город: Киев"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "datetime - python module \"datetime\""
msgstr "datetime - python модуль \"datetime\""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "dateutil - python module \"dateutil\""
msgstr "dateutil - python модуль \"dateutil\""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "env - environment"
msgstr "env - environment"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_blue
msgid "eval [name: Blue]"
msgstr "eval [название: Blue]"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_green
msgid "eval [name: Green]"
msgstr "eval [название: Green]"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_sx_corp
msgid "eval [name: SX Corp]"
msgstr "eval [название: SX Corp]"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_contact
msgid "eval [type: contact]"
msgstr "eval [тип: contact]"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_eval_error
msgid "eval with error"
msgstr "eval с ошибкой"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "model - model"
msgstr "model - модель"

#. module: generic_condition
#: model:generic.condition,name:generic_condition.demo_condition_partner_not_sx_corp
msgid "not [name: SX Corp]"
msgstr "не [название: SX Corp]"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "obj - object that is checked"
msgstr "obj - объект проверки"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "record - record of the object that is checked (same as obj)"
msgstr "record - запись объекта, который проверяется (то же что и obj)"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "time - python module \"time\""
msgstr "time - python модуль \"time\""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "timezone - python module \"timezone\""
msgstr "timezone - python модуль \"timezone\""

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "uid - current user id"
msgstr "uid - id текущего пользователя"

#. module: generic_condition
#: model_terms:ir.ui.view,arch_db:generic_condition.generic_condition_form_view
msgid "user - current user"
msgstr "user - текущий пользователь"

#~ msgid "Followers (Channels)"
#~ msgstr "Подписчики (Каналы)"

#~ msgid ""
#~ "Checking condition %s on document %s caused error. Notify administrator "
#~ "to fix it.\n"
#~ "\n"
#~ "---\n"
#~ "%s"
#~ msgstr ""
#~ "Проверка %s документа %s вызвала ошибку. Сообщите администратору для ее "
#~ "исправления.\n"
#~ "\n"
#~ "---\n"
#~ "%s"

#~ msgid "Error caught while evaluating condition %s[%d]"
#~ msgstr "Возникла ошибка при вычислении условия %s[%d]"

#~ msgid ""
#~ "Generic conditions misconfigured!\n"
#~ "object's model and condition's model does not match:\n"
#~ "\tcondition: %s [%d]\tobject: %s [%d]\tobject's model: %s\n"
#~ "\tcondition's model: %s\n"
#~ msgstr ""
#~ "Неправильно настроены универсальные условия!\n"
#~ "модели объекта и условия не совпадают:\n"
#~ "\tусловие: %s [%d]\tобъект: %s [%d]\tмодель объекта: %s\n"
#~ "\tмодель условия: %s\n"

#~ msgid ""
#~ "Incorrect Condition (condition group) selected!\n"
#~ "Base condition: %s[%s]\n"
#~ "Condition with wrong model: %s[%s]"
#~ msgstr ""
#~ "Выбрано некорректное условие (группа условий)!\n"
#~ "Основное условие: %s[%s]\n"
#~ "Условие с неверной моделью: %s[%s]"

#~ msgid "Incorrect Conditon field set for condition: %s[%s]"
#~ msgstr "Установлено некорректное поле условия для: %s[%s]"

#~ msgid "Incorrect Filter field set for condition: %s[%s]"
#~ msgstr "Установлено некорректное поле фильтра для условия: %s[%s]"

#~ msgid "Object (model: %s; id: %s) not found"
#~ msgstr "Объект (модель: %s; id: %s) не найден"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#~ msgid "Number of error"
#~ msgstr "Кол-во ошибок"
