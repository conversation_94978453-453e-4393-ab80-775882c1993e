image: registry.crnd.pro/crnd-opensource/docker/odoo-ci-x:15.0-simple

variables:
    SERIES_BRANCH: '15.0'
    ODOO_ADMIN_PASS: admin
    CI_JOB_TOKEN_GIT_HOST: 'gitlab.crnd.pro'

    PIP_CACHE_DIR: "${CI_PROJECT_DIR}/.hidden-pip-cache"

.only-stable-branch: &only_stable_branch
    only:
        - "15.0"

.only-dev-branch: &only_dev_branch
    only:
        - /^15.0-.*$/

.except-auto-commits: &except_auto_commits
    except:
        variables:
            - $CI_COMMIT_MESSAGE =~ /\[CI-AUTO-UPDATE\]/
            - $CI_COMMIT_MESSAGE =~ /Translated using Weblate/

# Disable global cache
cache: {}

.pip_cache: &pip_cache_definition
    cache:
        key: ${CI_JOB_NAME}
        paths:
            - "${PIP_CACHE_DIR}"

stages:
    - static-test
    - test
    - deploy

build_env:
    stage: static-test
    script:
        - pwd
        - whoami
        - odoo-helper --version
        - odoo-helper status --tools-versions --ci-tools-versions
        - echo "Checking branch compatability $CI_COMMIT_BRANCH" && [[ "$CI_COMMIT_BRANCH" =~ ^15.0(-.*)?$ ]]

flake8:
    stage: static-test
    script:
        - odoo-helper lint flake8 .
    <<: *except_auto_commits

pylint:
    stage: static-test
    script:
        - odoo-helper lint pylint --jslintrc=./.jslintrc .
    <<: *except_auto_commits

stylelint:
    stage: static-test
    script:
        - odoo-helper lint style .
    allow_failure: true
    <<: *except_auto_commits

bandit:
    stage: static-test
    <<: *pip_cache_definition
    before_script:
        - odoo-helper pip install bandit
    script:
        - odoo-helper exec bandit -r --skip=B410 .
    <<: *except_auto_commits

check-versions:
    stage: static-test
    script:
        - odoo-helper ci check-versions-git . origin/$SERIES_BRANCH $CI_COMMIT_SHA
    <<: *only_dev_branch
    <<: *except_auto_commits

ensure-changelog:
    stage: static-test
    script:
        - odoo-helper ci ensure-changelog --format md . origin/$SERIES_BRANCH $CI_COMMIT_SHA
    allow_failure: true
    <<: *only_dev_branch
    <<: *except_auto_commits

ensure-icons:
    stage: static-test
    script:
        - odoo-helper ci ensure-icons .
    allow_failure: true
    <<: *only_dev_branch
    <<: *except_auto_commits

tests:
    image: registry.crnd.pro/crnd-opensource/docker/odoo-ci-x:15.0-ci
    stage: test
    <<: *pip_cache_definition
    before_script:
        - odood addons link -r .
        - odood addons add --odoo-requirements odoo_requirements.txt
        - odood addons link -r .
        - sudo apt-get remove -y google-chrome-stable
        - sudo apt-get update -qq && wget -q https://dl.google.com/linux/chrome/deb/pool/main/g/google-chrome-stable/google-chrome-stable_127.0.6533.119-1_amd64.deb -O /tmp/chrome.deb && apt install -yqq --no-install-recommends --auto-remove /tmp/chrome.deb && rm /tmp/chrome.deb && rm -rf /var/lib/apt/lists/*
        - google-chrome --version
        # Install a specific version of Google Chrome (127.0.6533.119) to ensure stability
        # Version 128.0.6613.84 caused test tours to fail, so we're using 127 to avoid issues
    script:
        - odood test -t --isw --error-report --coverage --coverage-report --coverage-skip-covered --coverage-fail-under 30 --dir .
    <<: *except_auto_commits
    coverage: '/^TOTAL\s+\d+\s+\d+\s+(\d+\%)/'

migration_tests:
    image: registry.crnd.pro/crnd-opensource/docker/odoo-ci-x:15.0-ci
    stage: test
    variables:
        ODOO_DB_NAME: test-db
    <<: *pip_cache_definition
    before_script:
#        - git fetch origin $SERIES_BRANCH
        - odood addons link -r .
        - odood addons add --odoo-requirements odoo_requirements.txt
        - odood addons link -r .
        # Install a specific version of Google Chrome (127.0.6533.119) to ensure stability
        # Version 128.0.6613.84 caused test tours to fail, so we're using 127 to avoid issues
        - apt-get remove -y google-chrome-stable
        - apt-get update -qq && wget -q https://dl.google.com/linux/chrome/deb/pool/main/g/google-chrome-stable/google-chrome-stable_127.0.6533.119-1_amd64.deb -O /tmp/chrome.deb && apt install -yqq --no-install-recommends --auto-remove /tmp/chrome.deb && rm /tmp/chrome.deb && rm -rf /var/lib/apt/lists/*
        - google-chrome --version
    script:
        - odood test -t --migration --isw --error-report --coverage --coverage-report --coverage-skip-covered --coverage-fail-under=30 --dir .
    coverage: '/^TOTAL\s+\d+\s+\d+\s+(\d+\%)/'
    <<: *only_dev_branch
    <<: *except_auto_commits
    allow_failure: true

update_addons_list_and_trans:
    image: registry.crnd.pro/crnd-opensource/docker/odoo-ci-x:15.0-ci
    stage: deploy
    before_script:
        - odoo-helper link .
    script:
        - odoo-helper doc-utils addons-list --installable --sys-name --field name --field license --field version --field summary --field price --format csv . > ./ADDONS.csv
        - odoo-helper doc-utils addons-list --installable --sys-name --field name --field license --field version --field summary --field price --format md . > ./ADDONS.md
        - odoo-helper tr regenerate --lang-file "uk_UA:uk" --lang-file "ru_RU:ru" --lang-file "de_DE:de" --lang-file "fr_FR:fr" --lang-file "lt_LT:lt" --pot --pot-remove-dates --missing-only --pot-update --dir .
        - git add ./ADDONS.csv ./ADDONS.md "*.po" "*.pot"
        - odoo-helper ci push-changes "[CI-AUTO-UPDATE]"
    <<: *only_stable_branch
    <<: *except_auto_commits
    allow_failure: true

.deploy_review_definition: &deploy_review_definition
    stage: deploy
    before_script:
        - ci-review-app init
    script:
        - ci-review-app start
    tags:
        - deploy
        - odoo-review-ci4-shared
        - review-apps


deploy_review_dev:
    <<: *deploy_review_definition
    environment:
        name: review/$CI_COMMIT_REF_NAME
        url: http://$CI_ENVIRONMENT_SLUG-$CI_PROJECT_ID.ci4.crnd.pro
        on_stop: stop_review_dev
    <<: *only_dev_branch
    when: manual

deploy_review_stable:
    <<: *deploy_review_definition
    environment:
        name: stable/$CI_COMMIT_REF_NAME
        url: http://$CI_ENVIRONMENT_SLUG-$CI_PROJECT_ID.ci4.crnd.pro
    <<: *only_stable_branch

stop_review_dev:
    stage: deploy
    environment:
        name: review/$CI_COMMIT_REF_NAME
        action: stop
    variables:
        GIT_STRATEGY: none
    script:
        - ci-review-app stop
    when: manual
    tags:
        - deploy
        - odoo-review-ci4-shared
        - review-apps
    <<: *only_dev_branch
