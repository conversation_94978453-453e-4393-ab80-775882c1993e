| System Name | Name | License | Version | Summary | Price |
|---|---|---|---|---|---|
| crnd_web_actions | CRND web actions | LGPL-3 | ********.0 |  |  |
| crnd_web_button_box_full_width | CRND Web Button Box Fullwidth | LGPL-3 | ********.0 | Button_box at the top of the form |  |
| crnd_web_diagram_plus | CRnD Web Diagram Plus | LGPL-3 | *********.1 | Odoo Web Diagram view by CRnD. |  |
| crnd_web_field_domain | CRnD Web Field Domain | LGPL-3 | ********.0 | Web Field Domain by CRnD allows create computed field domains. |  |
| crnd_web_float_full_time_widget | CRND Web Float Full Time Widget | LGPL-3 | ********.0 | Float Time Duration Widget |  |
| crnd_web_list_popover_widget | CRND Web List Popover Widget | LGPL-3 | *********.0 | Tooltips message for text fields on tree view. |  |
| crnd_web_m2o_info_widget | Many2One Info Widget | LGPL-3 | *********.0 | Many2One Info Widget |  |
| crnd_web_map_view | CR&D Map View | LGPL-3 | ********.0 | This technical module provides view that allows to display objects on the map |  |
| crnd_web_on_create_action | CRND web on create action | LGPL-3 | ********.0 | Make it possible to use wizards to create records |  |
| crnd_web_tree_colored_field | CRND web tree colored field | LGPL-3 | ********.0 |  |  |
| crnd_web_tree_field_action | CRND web tree field action | LGPL-3 | ********.0 |  |  |
| crnd_web_view_refresh_timed | CRND web view refresh timed | LGPL-3 | ********.0 |  |  |
| crnd_web_widget_popup_image | CRND Web Image Popup Widget | LGPL-3 | ********.0 | Popup images from the binary fields |  |
| crnd_web_widget_scan_qrcode | CRND Web Widget Scan QR-Code | LGPL-3 | ********.1 | Scan QR-Code Widget |  |
| crnd_web_widget_section_and_note_text | CRND Web Widget Section And Note Text | LGPL-3 | 15.0.0.1.2 | Makes the standard section_and_note_text widget compatible with CRND Web List Popover Widget. |  |
| crnd_web_widget_select_geolocation | CRND Web Widget Select Geolocation | LGPL-3 | 15.0.0.1.3 | CRND Web Widget Select Geolocation |  |
| test_crnd_web_map_view | Test CR&D Map View | LGPL-3 | ********.2 |  |  |
| test_crnd_web_map_view_contacts | Test CR&D Map View Contacts | LGPL-3 | ********.1 |  |  |
| test_crnd_web_models | Test CRND Web Models | LGPL-3 | 15.0.0.15.0 | Module for testing web addons. |  |
