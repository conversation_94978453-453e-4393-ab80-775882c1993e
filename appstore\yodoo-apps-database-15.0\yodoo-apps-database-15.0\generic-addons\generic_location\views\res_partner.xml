<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_partner_form" model="ir.ui.view">
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form" />
        <field name="groups_id"
               eval="[(4, ref('generic_location.group_generic_location_user'))]"/>
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button class="oe_stat_button"
                        type="object"
                        name="action_show_related_generic_locations"
                        groups="generic_location.group_generic_location_user"
                        icon="fa-map-marker">
                    <field string="Locations" name="generic_location_count"
                           widget="statinfo"/>
                </button>
            </div>
        </field>
    </record>
</odoo>
