<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- User groups -->
    <record id="group_generic_location_user_implicit" model="res.groups">
        <field name="name">Generic location user (implicit)</field>
        <field name="category_id" ref="base.module_category_generic_location"/>
        <field name="comment">Access generic locations (no access to menu, access only to locations he is subscribed to, and theirs sublocations)
        </field>
    </record>
    <record id="group_generic_location_user" model="res.groups">
        <field name="name">Generic location user</field>
        <field name="implied_ids"
               eval="[(4, ref('generic_location.group_generic_location_user_implicit'))]"/>
        <field name="category_id" ref="base.module_category_generic_location"/>
        <field name="comment">Access generic locations</field>
    </record>
    <record id="group_generic_location_manager" model="res.groups">
        <field name="name">Generic location manager</field>
        <field name="implied_ids"
               eval="[(4, ref('generic_location.group_generic_location_user'))]"/>
        <field name="category_id" ref="base.module_category_generic_location"/>
        <field name="comment">Manage generic locations</field>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>
    <record id="group_use_hierarchical_locations" model="res.groups">
        <field name="name">Use hierarchical location</field>
        <field name="comment">Use hierarchical locations</field>
    </record>
    <!-- Rules-->
    <record id="rule_generic_location_user_implicit" model="ir.rule">
        <field name="name">User (implicit) generic locations</field>
        <field name="model_id" ref="generic_location.model_generic_location"/>
        <field name="domain_force">['|', ('message_partner_ids', 'child_of', user.commercial_partner_id.id), ('parent_ids.message_partner_ids', 'child_of', user.commercial_partner_id.id)]</field>
        <field name="groups" eval="[(4, ref('generic_location.group_generic_location_user_implicit'))]"/>
        <field eval="0" name="perm_unlink"/>
        <field eval="0" name="perm_write"/>
        <field eval="1" name="perm_read"/>
        <field eval="0" name="perm_create"/>
    </record>

    <record id="rule_generic_location_manager" model="ir.rule">
        <field name="name">Manager generic locations</field>
        <field name="model_id" ref="generic_location.model_generic_location"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('generic_location.group_generic_location_manager'))]"/>
        <field eval="1" name="perm_unlink"/>
        <field eval="1" name="perm_write"/>
        <field eval="1" name="perm_read"/>
        <field eval="1" name="perm_create"/>
    </record>
</odoo>
