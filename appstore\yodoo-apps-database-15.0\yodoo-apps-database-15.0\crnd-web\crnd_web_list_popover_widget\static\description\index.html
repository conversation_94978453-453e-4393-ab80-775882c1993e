<section style="max-width: 896px; margin: 0 auto;">
<div align="center">
<h1 style="color:#875A7B;">CRND Web List Popover Widget</h1>
</div>
<div align="center">
<p><a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png" /></a> <a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://img.shields.io/badge/maintainer-CR&amp;D-purple.png" /></a> <a href="https://github.com/crnd-inc/crnd-web/tree/11.0/crnd_web_list_popover_widget"><img alt="GitHub" src="https://img.shields.io/badge/GitHub-CRnD_Web_List_Popover_Widget-green.png"/></a></p>
<p><br></p>
</div>
</section>

<section style="margin: 0 auto; max-width: 896px;">
<div>
<p>Widget allows you to get text-overflow: ellipsis and popover for long Text, Char and HTML fields on the tree view. You can also set maximum width, number of text lines to show, popover position and fade animation on the tree view.
</p>
<p>Widget has the following options:</p>
<ul>
    <li><i>max_width</i> - string, max-width for field view (default 300px),</li>
    <li><i>popover_max_width</i> - string, max-width for popover (default 276px),</li>
    <li><i>animation</i> - string, 'True' or 'False'. Specifies whether to add a CSS fade transition effect when opening and closing the popover (default 'False'),</li>
    <li><i>placement</i> - string, specifies the popover position (default "auto"):
    <ul>
        <li><i>"top"</i>- Popover at top</li>
        <li><i>"bottom"</i>- Popover at bottom</li>
        <li><i>"left"</i>- Popover left</li>
        <li><i>"right"</i>- Popover right</li>
        <li><i>"auto"</i>- Lets the browser decide the position of the popover,</li>
    </ul>
    </li>
    <li><i>line_clamp</i> - string, number of multi strings for field view (default 1)
    <blockquote>NOTE: line_clamp option is not work for IE, it will always be 1.</blockquote>
    </li>
</ul>
<h3>How it works:</h3>
    <ol>
        <p></p>
        <li>
            <u>For Text fields</u>
            <br/><br/>
            Define a widget on the list view:
            <pre>&lt;field name="test_description_text" string="Description"
           widget="dynamic_popover_text"
           options="{'max_width': '350px', 'line_clamp': '3'}"/&gt;
            </pre>
            <p>Standard text:</p>
            <div align="center">
                <img src="text_1.png" alt="ellipsis" style="max-width: 100%">
                <p><br></p>
            </div>
            <p>Editable:</p>
            <div align="center">
                <img src="text_2.png" alt="edit mode" style="max-width: 100%">
                <p><br></p>
            </div>
        </li>
        <li>
            <u>For Char fields</u>
            <br/><br/>
            Define a widget on the list view:
            <pre>&lt;field name="test_description_char" string="Description"
           widget="dynamic_popover_char"
           options="{'max_width': '350px', 'line_clamp': '3'}"/&gt;
            </pre>
            <p>Standard text:</p>
            <div align="center">
                <img src="char_1.png" alt="ellipsis" style="max-width: 100%">
                <p><br></p>
            </div>
            <p>Editable:</p>
            <div align="center">
                <img src="char_2.png" alt="edit mode" style="max-width: 100%">
                <p><br></p>
            </div>
        </li>
        <li>
            <u>For HTML fields</u>
            <br/><br/>
            Define a widget on the list view:
            <pre>&lt;field name="test_description_html" string="Description"
           widget="dynamic_popover_html"
           options="{'max_width': '350px', 'line_clamp': '3'}"/&gt;
            </pre>
            <p>Standard text:</p>
            <div align="center">
                <img src="html_1.png" alt="ellipsis" style="max-width: 100%">
                <p><br></p>
            </div>
            <p>Editable:</p>
            <div align="center">
                <img src="html_2.png" alt="edit mode" style="max-width: 100%">
                <p><br></p>
            </div>
        </li>
    </ol>
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color:#875A7B;">Launch your own ITSM system in 60 seconds:</h2>
<p>Create your own <a href="https://yodoo.systems/saas/template/">Bureaucrat ITSM</a> database
<a href="https://yodoo.systems"><img alt="yodoo.systems" src="https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png"/></a>
</p> 
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color: #138c33">Maintainer</h2>
<a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://crnd.pro/web/image/3699/300x140/crnd.png"/></a>
<div align="center">
<a class="btn btn-primary mb16" title="Website" href="https://crnd.pro"><i class="fa fa-globe"></i> Website</a>
<a class="btn btn-primary mb16" title="Contact us" href="mailto:<EMAIL>"><i class="fa fa-envelope"></i> Contact us</a>
<a class="btn btn-primary mb16" title="Bug" href="https://crnd.pro/requests"><i class="fa fa-bug"></i> Bug</a>
<a class="btn btn-primary mb16" title="Requests" href="https://crnd.pro/requests"><i class="fa fa-bullhorn"></i> Requests</a>
</div>
<div align="center">
<a class="btn btn-primary mb16" title="Blog" href="https://crnd.pro/blog"><i class="fa fa-quote-right"></i> Blog</a>
<a class="btn btn-primary mb16" title="Youtube Channel" href="https://www.youtube.com/channel/UCRSGFeR0NgqflPmzpDjL2tw"><i class="fa fa-youtube-play"></i> Youtube Channel</a>
<p><br></p>
</div>

<p>This module is maintained by the <a href="https://crnd.pro/">Center of Research &amp; Development</a>
company.
</p>
<p>
We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you. 
</p>
</div>
</section>
