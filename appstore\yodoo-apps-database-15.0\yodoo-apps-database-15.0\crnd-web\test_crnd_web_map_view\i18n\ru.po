# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* test_crnd_web_map_view
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-14 13:44+0000\n"
"PO-Revision-Date: 2023-07-14 13:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: test_crnd_web_map_view
#: model:ir.ui.menu,name:test_crnd_web_map_view.crnd_web_map_view_test_root
msgid "CRND Web Map View Test"
msgstr ""

#. module: test_crnd_web_map_view
#: model_terms:ir.ui.view,arch_db:test_crnd_web_map_view.test_map_wizard_view_form_view
msgid "Cancel"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__create_date
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__create_date
msgid "Created on"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__display_name
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__id
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__id
msgid "ID"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view____last_update
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard____last_update
msgid "Last Modified on"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__write_date
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__latitude
msgid "Latitude"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__location_ids
msgid "Location"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__longitude
msgid "Longitude"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.ui.menu,name:test_crnd_web_map_view.crnd_web_map_view_test_main
msgid "Map View"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__name
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_wizard__name
msgid "Name"
msgstr ""

#. module: test_crnd_web_map_view
#: model_terms:ir.ui.view,arch_db:test_crnd_web_map_view.test_map_wizard_view_form_view
msgid "Open map"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__point_draggable_json
msgid "Point Draggable Json"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__point_readonly_json
msgid "Point Readonly Json"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model.fields,field_description:test_crnd_web_map_view.field_test_map_view__point_simple_json
msgid "Point Simple Json"
msgstr ""

#. module: test_crnd_web_map_view
#: model_terms:ir.ui.view,arch_db:test_crnd_web_map_view.test_map_view_form_view
#: model_terms:ir.ui.view,arch_db:test_crnd_web_map_view.test_map_view_tree_view
msgid "Point draggable"
msgstr ""

#. module: test_crnd_web_map_view
#: model_terms:ir.ui.view,arch_db:test_crnd_web_map_view.test_map_view_form_view
msgid "Point readonly"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.actions.act_window,name:test_crnd_web_map_view.action_test_map_view
#: model:ir.actions.act_window,name:test_crnd_web_map_view.action_test_map_view_only
#: model:ir.ui.menu,name:test_crnd_web_map_view.crnd_web_map_view_test
msgid "Test map view"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.actions.act_window,name:test_crnd_web_map_view.action_test_map_wizard
#: model:ir.ui.menu,name:test_crnd_web_map_view.crnd_web_map_wizard_test
msgid "Test map wizard"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model,name:test_crnd_web_map_view.model_test_map_view
msgid "test.map.view"
msgstr ""

#. module: test_crnd_web_map_view
#: model:ir.model,name:test_crnd_web_map_view.model_test_map_wizard
msgid "test.map.wizard"
msgstr ""
