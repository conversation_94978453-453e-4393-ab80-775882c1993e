<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Generic Condition Views -->
    <record id="generic_condition_search_view" model="ir.ui.view">
        <field name="model">generic.condition</field>
        <field name="arch" type="xml">
            <search string="Condition search">
                <field name="name"/>
                <field name="model_id"/>

                <separator/>
                <filter name="with_sudo"
                        domain="[('with_sudo', '=', True)]"
                        string="With sudo"/>
                <filter name="type_eval"
                        domain="[('type', '=', 'eval')]"
                        string="Expression"/>
                <filter name="type_filter"
                        domain="[('type', '=', 'filter')]"
                        string="Filter"/>

                <filter name="type_condition"
                        domain="[('type', '=', 'condition')]"
                        string="Condition"/>
                <filter name="type_related_conditions"
                        domain="[('type', '=', 'related_conditions')]"
                        string="Related conditions"/>
                <filter name="type_date_diff"
                        domain="[('type', '=', 'date_diff')]"
                        string="Date difference"/>
                <filter name="type_condition_group"
                        domain="[('type', '=', 'condition_group')]"
                        string="Condition group"/>
                <separator/>

                <group expand="0" string="Group by...">
                    <filter string='Type'
                            name="group_by_type"
                            domain="[]"
                            context="{'group_by' : 'type'}"/>
                    <filter string='With sudo'
                            name="group_by_with_sudo"
                            domain="[]"
                            context="{'group_by' : 'with_sudo'}"/>
                    <filter string='Based on'
                            name="group_by_based_on"
                            domain="[]"
                            context="{'group_by' : 'model_id'}"/>
                </group>

            </search>
        </field>
    </record>

    <record id="generic_condition_tree_view" model="ir.ui.view">
        <field name="model">generic.condition</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="sequence" invisible="1"/>
                <field name="type"/>
                <field name="model_id"/>
                <field name="enable_caching"/>
                <field name="with_sudo"/>
            </tree>
        </field>
    </record>

    <record id="generic_condition_form_view" model="ir.ui.view">
        <field name="model">generic.condition</field>
        <field name="arch" type="xml">
            <form string="Condition">
                <sheet>
                    <field name="sequence" invisible="1"/>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_show_test_wizard"
                                type="object"
                                class="oe_stat_button"
                                icon="fa-check-circle-o"
                                string="Test"/>
                        <button name="toggle_active" type="object"
                                class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button"
                                   options='{"terminology": "archive"}'/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="model_id"/>
                            <field name="based_on" invisible="1"/>
                            <field name="type"/>
                        </group>
                        <group>
                            <field name="enable_caching"/>
                            <field name="invert"/>
                            <field name="with_sudo"/>
                        </group>
                    </group>
                    <group string="Condition" colspan="4">

                        <!-- 'Filter' condition data -->
                        <group colspan="4" name="group_condition_filter"
                               attrs="{'invisible': [('type', '!=', 'filter')]}">

                            <div class="alert alert-warning" role="alert" colspan="4">
                                <p><b>Warning:</b> This feature is deprecated,
                                because filters cannot handle NewId records that used
                                during onchange computation.</p>
                                <p>Please, change this condition to use other types.</p>
                            </div>
                            <div colspan="4">
                                Here you can specify the filter for verification. <br />
                                <br />
                            </div>

                            <field name="condition_filter_id"
                                   domain="[('model_id','=',based_on)]"
                                   context="{'default_model_id': based_on}"
                                   attrs="{'required': [('type','=','filter')]}"/>
                        </group>

                        <!-- 'Condition' condition data -->
                        <group colspan="4" name="group_condition_condition"
                               attrs="{'invisible': [('type', '!=', 'condition')]}">

                            <div colspan="4">
                                In that case you can select one of the already created conditions (this could be useful if you use the same condition but with inversion or as a superuser). <br />
                                <br />
                            </div>     

                            <field name="condition_condition_id"
                                   attrs="{'required': [('type','=','condition')]}"
                                   context="{'default_model_id': model_id}"
                                   domain="[('model_id','=',model_id)]"/>
                        </group>

                        <!-- 'Eval' condition data -->
                        <group colspan="4" name="group_condition_eval"
                               attrs="{'invisible': [('type', '!=', 'eval')]}">

                            <div colspan="4">
                                By this type you can describe the condition as an expression in the Python programming language. <br />
                                <br />
                            </div>

                            <field name="condition_eval"
                                   widget="ace" options="{'mode': 'python'}"
                                   attrs="{'required': [('type','=','eval')]}"/>

                            <div colspan="4">
                                The following variables are available:
                                <ul>
                                    <li>obj - object that is checked</li>
                                    <li>record - record of the object that is checked (same as obj)</li>
                                    <li>env - environment</li>
                                    <li>model - model</li>
                                    <li>uid - current user id</li>
                                    <li>user - current user</li>
                                    <li>time - python module "time"</li>
                                    <li>datetime - python module "datetime"</li>
                                    <li>dateutil - python module "dateutil"</li>
                                    <li>timezone - python module "timezone"</li>
                                </ul>
                            </div>

                        </group>

                        <!-- 'Condition group' condition data -->
                        <group colspan="4" name="group_condition_group"
                               attrs="{'invisible': [('type', '!=', 'condition_group')]}">

                            <div colspan="4">
                                With this type of condition you can use multiple conditions simultaneously. <br />
                                <br /> 
                            </div>

                            <field name="condition_condition_ids_operator"
                                   attrs="{'required': [('type','=','condition_group')]}"/>
                            <field name="condition_condition_ids"
                                   attrs="{'required': [('type','=','condition_group')]}"
                                   context="{'default_model_id': model_id}"
                                   domain="[('model_id','=',model_id)]"
                                   widget="many2many_tags"
                                   options="{'no_quick_create': true, 'open': true}"/>

                        </group>

                        <!-- 'Related condition' condition data -->
                        <group colspan="4" col="4"
                               name="group_condition_related_conditions"
                               attrs="{'invisible': [('type', '!=', 'related_conditions')]}">

                            <div colspan="4">
                                This type of condition is used to check the properties of related objects. <br />
                                <br />
                            </div>

                            <table colspan="4" class="generic_condition_definition">
                                <tr>
                                    <td>
                                        <label for="condition_rel_field_id"/>
                                    </td>
                                    <td>
                                        <field name="condition_rel_record_operator"
                                               attrs="{'required': [('type','=','related_conditions')]}"/>
                                    </td>
                                    <td>
                                        <field name="condition_rel_field_id"
                                               attrs="{'required': [('type','=','related_conditions')]}"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id),('ttype', 'in', ('many2many', 'one2many', 'many2one'))]"/>
                                        <field name="condition_rel_field_id_model_id" invisible="1"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label for="condition_rel_filter_conditions"/>
                                    </td>
                                    <td>
                                        <field name="condition_rel_filter_conditions_operator"/></td>
                                    <td>
                                        <field name="condition_rel_filter_conditions"
                                               context="{'default_model_id': condition_rel_field_id_model_id}"
                                               domain="[('model_id','=',condition_rel_field_id_model_id)]"
                                               widget="many2many_tags"
                                               options="{'no_quick_create': true, 'open': true}"/></td>
                                </tr>
                                <tr>
                                    <td>
                                        <label for="condition_rel_conditions"/></td>
                                    <td>
                                        <field name="condition_rel_conditions_operator"
                                               attrs="{'required': [('type','=','related_conditions')]}"/></td>
                                    <td>
                                        <field name="condition_rel_conditions"
                                               attrs="{'required': [('type','=','related_conditions')]}"
                                               context="{'default_model_id': condition_rel_field_id_model_id}"
                                               domain="[('model_id','=',condition_rel_field_id_model_id)]"
                                               widget="many2many_tags"
                                               options="{'no_quick_create': true, 'open': true}"/></td>
                                </tr>
                            </table>
                        </group>

                        <!-- 'Current user' condition data -->
                        <group colspan="4" col="4"
                               name="group_condition_current_user"
                               attrs="{'invisible': [('type', '!=', 'current_user')]}">

                            <div colspan="4">
                                <p>This type of condition refers to the current user and verifies whether the current user satisfies conditions.</p>
                                <p>There following check types</p>
                                <ul>
                                    <li>Field: Check if current user is present in field of object</li>
                                    <li>One of: Check if current user is present in specified list of users</li>
                                    <li>Checks: Check if current user satisfies specified conditions</li>
                                </ul>
                            </div>

                            <group colspan="2">
                                <field name="condition_user_check_type"
                                    attrs="{'required': [('type','=','current_user')]}"/>
                            </group>
                            <group colspan="2">
                                <field name="condition_user_user_field_id"
                                       attrs="{'required': [('type','=','current_user'), ('condition_user_check_type', '=', 'field')], 'invisible': ['|', ('type','!=','current_user'), ('condition_user_check_type', '!=', 'field')]}"
                                       domain="[('model_id','=',model_id),
                                                ('ttype', 'in', ('many2one', 'one2many', 'many2many')),
                                                ('relation', '=', 'res.users')]"/>
                                <field name="condition_user_one_of_user_ids"
                                       attrs="{'required': [('type','=','current_user'), ('condition_user_check_type', '=', 'one_of')], 'invisible': ['|', ('type','!=','current_user'), ('condition_user_check_type', '!=', 'one_of')]}"
                                       widget="many2many_tags"/>
                                <field name="condition_user_checks_condition_ids"
                                       attrs="{'required': [('type','=','current_user'), ('condition_user_check_type', '=', 'checks')], 'invisible': ['|', ('type','!=','current_user'), ('condition_user_check_type', '!=', 'checks')]}"
                                       context="{'default_based_on': 'res.users'}"
                                       widget="many2many_tags"/>
                            </group>
                        </group>

                        <!-- 'Date diff' condition data -->
                        <group colspan="4" col="4"
                               name="group_condition_date_diff"
                               attrs="{'invisible': [('type', '!=', 'date_diff')]}">

                            <div colspan="4">
                                This type of the condition allows you to estimate the date difference between certain events. <br />
                                <br />
                            </div>

                            <table colspan="4" class="generic_condition_definition">
                                <tr>
                                    <td>
                                        <label for="condition_date_diff_date_end_type" string="End Date"/>
                                    </td>
                                    <td>
                                        &#160; &#160; &#160; &#160;
                                    </td>
                                    <td>
                                        <label for="condition_date_diff_date_start_type" string="Start Date"/>
                                    </td>
                                    <td>
                                        <label for="condition_date_diff_operator" string="Operator"/>
                                    </td>
                                    <td>
                                        <label for="condition_date_diff_value" string="Result"/>
                                    </td>
                                    <td>
                                        <label for="condition_date_diff_absolute"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <field name="condition_date_diff_date_end_type"
                                               attrs="{'required': [('type','=','date_diff')]}"/>
                                    </td>
                                    <td rowspan="2">
                                        &#160; &#8211; &#160;
                                    </td>
                                    <td>
                                        <field name="condition_date_diff_date_start_type"
                                               attrs="{'required': [('type','=','date_diff')]}"/>
                                    </td>
                                    <td rowspan="2">
                                        <field name="condition_date_diff_operator"
                                               attrs="{'required': [('type','=','date_diff')]}"/>
                                    </td>
                                    <td rowspan="2">
                                        <field name="condition_date_diff_value" class="oe_inline"
                                               attrs="{'required': [('type','=','date_diff')]}"/>
                                        <field name="condition_date_diff_uom" class="oe_inline"
                                               attrs="{'required': [('type','=','date_diff')]}"/>
                                    </td>
                                    <td rowspan="2">
                                        <field name="condition_date_diff_absolute"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <field name="condition_date_diff_date_end_field"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id),('ttype', 'in', ('datetime', 'date'))]"
                                               attrs="{'required': [('type','=','date_diff'), ('condition_date_diff_date_end_type', '=','field')], 'invisible': [('condition_date_diff_date_end_type', '!=', 'field')]}"/>
                                        <field name="condition_date_diff_date_end_date"
                                               attrs="{'required': [('type','=','date_diff'), ('condition_date_diff_date_end_type', '=','date')], 'invisible': [('condition_date_diff_date_end_type', '!=', 'date')]}"/>
                                        <field name="condition_date_diff_date_end_datetime"
                                               attrs="{'required': [('type','=','date_diff'), ('condition_date_diff_date_end_type', '=','datetime')], 'invisible': [('condition_date_diff_date_end_type', '!=', 'datetime')]}"/>
                                        <div attrs="{'invisible': [('condition_date_diff_date_end_type', '!=', 'now')]}">
                                            Current date
                                        </div>
                                    </td>
                                    <td>
                                        <field name="condition_date_diff_date_start_field"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id),('ttype', 'in', ('datetime', 'date'))]"
                                               attrs="{'required': [('type','=','date_diff'), ('condition_date_diff_date_start_type', '=','field')], 'invisible': [('condition_date_diff_date_start_type', '!=', 'field')]}"/>
                                        <field name="condition_date_diff_date_start_date"
                                               attrs="{'required': [('type','=','date_diff'), ('condition_date_diff_date_start_type', '=','date')], 'invisible': [('condition_date_diff_date_start_type', '!=', 'date')]}"/>
                                        <field name="condition_date_diff_date_start_datetime"
                                               attrs="{'required': [('type','=','date_diff'), ('condition_date_diff_date_start_type', '=','datetime')], 'invisible': [('condition_date_diff_date_start_type', '!=', 'datetime')]}"/>
                                        <div attrs="{'invisible': [('condition_date_diff_date_start_type', '!=', 'now')]}">
                                            Current date
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </group>

                        <!-- 'Simple field' condition data -->
                        <group colspan="4" col="4"
                               name="group_condition_simple_field"
                               attrs="{'invisible': [('type', '!=', 'simple_field')]}">

                            <div colspan="4">
                                Here you can create a condition that relates to the simple field of the Odoo object. <br />
                                <br />
                            </div>

                            <span><field name="condition_simple_field_type"
                                         invisible="1"/></span>
                            <table colspan="4" class="generic_condition_definition">
                                <tr>
                                    <td>
                                        <label for="condition_simple_field_field_id" string="Field"/>
                                    </td>
                                    <td>
                                        <label for="condition_simple_field_number_operator" string="Operator"/>
                                    </td>
                                    <td>
                                        <label for="condition_simple_field_value_char" string="Value"
                                               attrs="{'invisible': [('condition_simple_field_type', '=', 'boolean')]}"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <field name="condition_simple_field_field_id"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id), ('ttype', 'in', ('boolean', 'char', 'text', 'html', 'float', 'integer', 'selection', 'date', 'datetime'))]"
                                               attrs="{'required': [('type','=','simple_field')]}"/>
                                    </td>
                                    <td>
                                        <field name="condition_simple_field_selection_operator"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'selection')], 'invisible': [('condition_simple_field_type', '!=', 'selection')]}"/>
                                        <field name="condition_simple_field_number_operator"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', 'in', ('float', 'integer'))], 'invisible': [('condition_simple_field_type', 'not in', ('float', 'integer'))]}"/>
                                        <field name="condition_simple_field_string_operator"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', 'in', ('char', 'text'))], 'invisible': [('condition_simple_field_type', 'not in', ('char', 'text'))]}"/>
                                        <field name="condition_simple_field_string_operator_html"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'html')], 'invisible': [('condition_simple_field_type', '!=', 'html')]}"/>
                                        <field name="condition_simple_field_date_operator"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', 'in', ('date', 'datetime'))], 'invisible': [('condition_simple_field_type', 'not in', ('date', 'datetime'))]}"/>
                                        <div>
                                            <span
                                                    attrs="{'invisible': ['|', ('condition_simple_field_type', 'not in', ('char', 'text', 'html')),('condition_simple_field_string_operator', 'in', ('set', 'not set'))]}">
                                                <field name="condition_simple_field_string_operator_icase"/>
                                                <label for="condition_simple_field_string_operator_icase"/>
                                            </span>
                                        </div>
                                        <div>
                                            <span
                                                    attrs="{'invisible': ['|', ('condition_simple_field_type', 'not in', ('char', 'text', 'html')),('condition_simple_field_string_operator', 'in', ('set', 'not set'))]}">
                                                <field name="condition_simple_field_string_operator_regex"/>
                                                <label for="condition_simple_field_string_operator_regex"/>
                                            </span>
                                        </div>
                                        <field name="condition_simple_field_value_boolean"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'boolean')], 'invisible': [('condition_simple_field_type', '!=', 'boolean')]}"/>
                                    </td>
                                    <td>
                                        <field name="condition_simple_field_value_char"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', 'in', ('char', 'text', 'html')), ('condition_simple_field_string_operator', 'not in', ('set', 'not set'))], 'invisible': ['|', ('condition_simple_field_type', 'not in', ('char', 'text', 'html')), ('condition_simple_field_string_operator', 'in', ('set', 'not set'))]}"/>
                                        <field name="condition_simple_field_value_integer"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'integer')], 'invisible': [('condition_simple_field_type', '!=', 'integer')]}"/>
                                        <field name="condition_simple_field_value_float"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'float')], 'invisible': [('condition_simple_field_type', '!=', 'float')]}"/>
                                        <field name="condition_simple_field_value_selection"
                                               widget="fake_selection"
                                               selection_field="condition_simple_field_field_id"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'selection')], 'invisible': ['|', ('condition_simple_field_type', '!=', 'selection'), ('condition_simple_field_selection_operator', 'in', ('set', 'not set'))]}"/>
                                        <field name="condition_simple_field_value_date"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'date'), ('condition_simple_field_date_operator', 'not in', ('set', 'not set'))], 'invisible': ['|', ('condition_simple_field_type', '!=', 'date'),('condition_simple_field_date_operator', 'in', ('set', 'not set'))]}"/>
                                        <field name="condition_simple_field_value_datetime"
                                               attrs="{'required': [('type','=','simple_field'), ('condition_simple_field_type', '=', 'datetime'), ('condition_simple_field_date_operator', 'not in', ('set', 'not set'))], 'invisible': ['|', ('condition_simple_field_type', '!=', 'datetime'),('condition_simple_field_date_operator', 'in', ('set', 'not set'))]}"/>
                                    </td>
                                </tr>
                            </table>
                        </group>
                        <!-- 'Related field' condition data -->
                        <group colspan="4" col="4"
                               name="group_condition_related_field"
                               attrs="{'invisible': [('type', '!=', 'related_field')]}">

                            <div colspan="4">
                                This type of condition applies to related fields. <br />
                                <br />
                            </div>

                            <span><field name="condition_related_field_model"
                                         invisible="1"/></span>
                            <table colspan="4" class="generic_condition_definition">
                                <tr>
                                    <td>

                                        <label for="condition_related_field_field_id" string="Field"/>
                                    </td>
                                    <td>
                                        <label for="condition_related_field_operator" string="Operator"/>
                                    </td>
                                    <td>
                                        <label for="condition_related_field_value_id"
                                               string="Value"
                                               attrs="{'invisible': [('condition_related_field_operator', 'in', ('set', 'not set'))]}"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <field name="condition_related_field_field_id"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id), ('ttype', 'in', ('many2one', 'many2many'))]"
                                               attrs="{'required': [('type','=','related_field')]}"/>
                                    </td>
                                    <td>
                                        <field name="condition_related_field_operator"
                                               attrs="{'required': [('type','=','related_field')]}"/>
                                    </td>
                                    <td>
                                        <field name="condition_related_field_value_id"
                                               widget="generic_m2o" model_field="condition_related_field_model"
                                               attrs="{'required': [('type','=','related_field'), ('condition_related_field_operator', '=', 'contains')], 'invisible': [('condition_related_field_operator', 'in', ('set', 'not set'))]}"/>
                                    </td>
                                </tr>
                            </table>
                        </group>
                        <!-- 'Monetary field' condition data -->
                        <group colspan="4" col="4"
                               name="group_condition_monetary_field"
                               attrs="{'invisible': [('type', '!=', 'monetary_field')]}">

                            <div colspan="4">
                                By this type you can create the condition to compare monetary indicators. <br />
                                <br />
                            </div>

                            <table colspan="4" class="generic_condition_definition">
                                <tr>
                                    <td>
                                        <label for="condition_monetary_field_id" string="Field"/>
                                    </td>
                                    <td>
                                        <label for="condition_monetary_operator" string="Operator"/>
                                    </td>
                                    <td>
                                        <label for="condition_monetary_value" string="Result"/>
                                    </td>
                                    <td>
                                        <label for="condition_monetary_curency_date_type" string="Accounting date"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <field name="condition_monetary_field_id"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id), ('ttype', '=', 'monetary')]"
                                               attrs="{'required': [('type','=','monetary_field')]}"
                                               placeholder="Field"
                                               options="{'no_create': True}"/>
                                    </td>
                                    <td>
                                        <field name="condition_monetary_operator"
                                               attrs="{'required': [('type','=','monetary_field')]}"/>
                                    </td>
                                    <td>
                                        <field name="condition_monetary_value"
                                               attrs="{'required': [('type', '=', 'monetary_field')]}"/>
                                    </td>
                                    <td>
                                        <field name="condition_monetary_curency_date_type"
                                               attrs="{'required': [('type','=','monetary_field')]}"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <field name="condition_monetary_currency_field_id"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id), ('relation', '=', 'res.currency')]"
                                               attrs="{'required': [('type','=','monetary_field')]}"
                                               placeholder="Currency"
                                               options="{'no_create': True}"/>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        <field name="condition_monetary_value_currency_id"
                                               attrs="{'required': [('type', '=', 'monetary_field')]}"
                                               placeholder="Currency"
                                               options="{'no_create': True}"/>
                                    </td>
                                    <td>
                                        <field name="condition_monetary_curency_date_field_id"
                                               placeholder="Field"
                                               options="{'no_create': True}"
                                               context="{'default_model_id': model_id}"
                                               domain="[('model_id','=',model_id),('ttype', 'in', ('datetime', 'date'))]"
                                               attrs="{'required': [('type','=','monetary_field'), ('condition_monetary_curency_date_type', '=','field')], 'invisible': [('condition_monetary_curency_date_type', '!=', 'field')]}"/>
                                        <field name="condition_monetary_curency_date_date"
                                               attrs="{'required': [('type','=','monetary_field'), ('condition_monetary_curency_date_type', '=','date')], 'invisible': [('condition_monetary_curency_date_type', '!=', 'date')]}"/>
                                        <label string="Current date"
                                               for="condition_monetary_curency_date_date"
                                               attrs="{'invisible': [('condition_monetary_curency_date_type', '!=', 'now')]}"/>
                                    </td>
                                </tr>
                            </table>
                        </group>
                        <!-- 'Monetary field' condition data -->
                        <group colspan="4" col="4"
                               name="group_condition_find"
                               attrs="{'invisible': [('type', '!=', 'find')]}">
                            <div class="alert alert-warning" role="alert" colspan="4">
                                <b>Warning:</b> This feature is experimental.
                            </div>
                            <div colspan="4">
                                This type of condition can be used to find related objects in arbitary table
                                and check them.
                            </div>
                            <group name="generic_condition_find_search"
                                   string="Search"
                                   colspan="2">
                                <field name="condition_find_search_model_id"
                                       attrs="{'required': [('type','=','find')]}"
                                       string="Model"/>
                                <field name="condition_find_search_model_name"
                                       string="Model Name"/>
                                <field name="condition_find_order_by_field_id"
                                       domain="[('model_id', '=', condition_find_search_model_id), ('store', '=', True)]"
                                       attrs="{'required': [('type','=','find')]}"
                                       string="Order By Field"/>
                                <field name="condition_find_order_by_direction"
                                       attrs="{'required': [('type','=','find')]}"
                                       string="Order By Direction"/>

                            </group>
                            <group name="generic_condition_find_check"
                                   string="Check"
                                   colspan="2">
                                <field name="condition_find_if_not_found"
                                       attrs="{'required': [('type','=','find')]}"
                                       string="If records not found"/>
                                <field name="condition_find_fetch_type"
                                       attrs="{'required': [('type','=','find')]}"
                                       string="Type"/>
                                <field name="condition_find_check_condition_ids"
                                       context="{'default_model_id': condition_find_search_model_id}"
                                       domain="[('model_id', '=', condition_find_search_model_id)]"
                                       attrs="{'required': [('type','=','find')]}"
                                       widget="many2many_tags"
                                       string="Conditions"/>
                            </group>
                            <group colspan="4"
                                   name="generic_condition_find_domain"
                                   string="Filter records">
                                <field name="condition_find_search_domain_ids"
                                       attrs="{'required': [('type','=','find')]}"
                                       mode="tree,form"
                                       nolabel="1"
                                       colspan="2">
                                    <tree>
                                        <field name="sequence" widget="handle"/>
                                        <field name="type"/>
                                        <field name="check_field_id"
                                               domain="[('model_id', '=', parent.condition_find_search_model_id),('store', '=', True)]"
                                               attrs="{'required': [('type', '=', 'search-condition')],'invisible': [('type', '!=', 'search-condition')]}"/>
                                        <field name="value_field_operator"
                                               attrs="{'required': [('type', '=', 'search-condition')],'invisible': [('type', '!=', 'search-condition')]}"/>
                                        <field name="value_display"
                                               attrs="{'invisible': [('type', '!=', 'search-condition')]}"/>
                                    </tree>
                                    <form>
                                        <group>
                                            <group>
                                                <field name="type"/>
                                            </group>
                                            <group>
                                                <field name="sequence"/>
                                            </group>
                                        </group>
                                        <group name="group_leaf"
                                               string="Search condition"
                                               attrs="{'invisible': [('type', '!=', 'search-condition')]}">
                                            <table colspan="4" class="generic_condition_definition">
                                            <tr>
                                                <th>
                                                    <label for="check_field_id" string="Check Field"/>
                                                </th>
                                                <th>
                                                    <label for="value_field_operator" string="Operator"/>
                                                </th>
                                                <th>
                                                    <label for="value_char" string="Value"/>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <field name="check_field_id"
                                                           domain="[('model_id', '=', parent.condition_find_search_model_id),('store', '=', True)]"
                                                           context="{'default_model_id': parent.condition_find_search_model_id}"
                                                           attrs="{'required': [('type', '=', 'search-condition')],'invisible': [('type', '!=', 'search-condition')]}"/>
                                                    <field name="check_field_type" invisible="1"/>
                                                    <field name="check_field_relation" invisible="1"/>
                                                </td>
                                                <td>
                                                    <field name="value_field_operator"
                                                           attrs="{'required': [('type', '=', 'search-condition')],'invisible': [('type', '!=', 'search-condition')]}"/>
                                                </td>
                                                <td>
                                                    <!-- Fields to check static value -->
                                                    <field name="value_type"
                                                           attrs="{'required': [('type','=','search-condition')], 'invisible': [('type', '!=', 'search-condition')]}"/>
                                                    <field name="value_char"
                                                           attrs="{'required': [('value_type','=','static-value'), ('check_field_type', 'in', ('char', 'text', 'html'))], 'invisible': ['|', ('value_type', '!=', 'static-value'), ('check_field_type', 'not in', ('char', 'text', 'html'))]}"/>
                                                    <field name="value_integer"
                                                           attrs="{'required': [('value_type','=','static-value'), ('check_field_type', '=', 'integer')], 'invisible': ['|', ('value_type', '!=', 'static-value'), ('check_field_type', '!=', 'integer')]}"/>
                                                    <field name="value_float"
                                                           attrs="{'required': [('value_type','=','static-value'), ('check_field_type', '=', 'float')], 'invisible': ['|', ('value_type', '!=', 'static-value'), ('check_field_type', '!=', 'float')]}"/>
                                                    <!-- TODO: Allow only selection and res_id static checks to simplify code -->
                                                    <field name="value_selection"
                                                           widget="fake_selection"
                                                           selection_field="check_field_id"
                                                           attrs="{'required': [('value_type','=','static-value'), ('check_field_type', '=', 'selection')], 'invisible': ['|', ('value_type', '!=', 'static-value'), ('check_field_type', '!=', 'selection')]}"/>
                                                    <field name="value_res_id"
                                                           widget="generic_m2o"
                                                           model_field="check_field_relation"
                                                           attrs="{'required': [('value_type','=','static-value'), ('check_field_type', 'in', ('many2one', 'one2many', 'many2many'))], 'invisible': ['|', ('value_type', '!=', 'static-value'), ('check_field_type', 'not in', ('many2one', 'one2many', 'many2many'))]}"/>

                                                    <!-- Fields to check against object field -->
                                                    <field name="value_field_id"
                                                           attrs="{'required': [('value_type', '=', 'object-field')],'invisible': [('value_type', '!=', 'object-field')]}"
                                                           domain="[('model_id', '=', parent.model_id), ('ttype', '=', check_field_type)]"/>
                                                </td>
                                            </tr>
                                            </table>
                                        </group>
                                    </form>
                                </field>
                            </group>
                        </group>
                    </group>
                    <group colspan="4" string="Description">
                        <field name="description"
                               nolabel="1"
                               placeholder="Description..."/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="generic_condition_action" model="ir.actions.act_window">
        <field name="name">Generic Conditions</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">generic.condition</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!--menus -->
    <menuitem id="menu_generic_condition"
              name="Generic Conditions"
              parent="generic_rule.menu_generic_rule" sequence="60"
              groups="generic_condition.group_generic_condition_manage"
              action="generic_condition_action"/>
</odoo>
