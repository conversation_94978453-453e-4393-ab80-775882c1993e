# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* test_crnd_web_models
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-20 12:33+0000\n"
"PO-Revision-Date: 2020-10-20 12:33+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.test_crnd_web_actions_view_form
msgid "Action editable"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.test_crnd_web_actions_view_form
msgid "Action readonly"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.test_crnd_web_actions_action_window
msgid "Actions"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__arrow_ids
msgid "Arrows"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__line_bg_color
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__res_bg_color
msgid "Backgroung Color"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__bg_color
msgid "Bg Color"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__bg_state
msgid "Bg State"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_book_wizard_create
msgid "Book wizard: Create"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.book_action_window
msgid "Books"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.car_rental_brand_action
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__brand_id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__brand_id
msgid "Brand"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__brand_id_field_domain
msgid "Brand Id Field Domain"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__name
msgid "Brand Name"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.crnd_web_tests
msgid "CRND Web Tests"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.example_wizard_create_form_view
msgid "Cancel"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.car_rental_car_action
#: model:ir.model,name:test_crnd_web_models.model_test_car_rental_car
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__car_id
msgid "Car"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_test_car_rental_brand
msgid "Car Brand"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__car_id_field_domain
msgid "Car Id Field Domain"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_test_car_rental_model
msgid "Car Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__name
msgid "Car Name"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.car_rental_action
#: model:ir.model,name:test_crnd_web_models.model_test_car_rental
#: model:ir.ui.menu,name:test_crnd_web_models.test_car_rental_menu
msgid "Car Rental"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__car_ids
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__car_ids
#: model:ir.ui.menu,name:test_crnd_web_models.test_car_rental_car_menu
msgid "Cars"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__char_field_simple
msgid "Char Field Simple"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__char_popover_widget
msgid "Char Popover Widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_popover_widget_char_model__char_field_simple
msgid "Char field no widgets."
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_popover_widget_char_model__char_popover_widget
msgid "Char field with char popover widget."
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_tree_colored_field__name
msgid "Color demonstration field"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_res_partner
msgid "Contact"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_wizard_book_create
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.example_wizard_create_form_view
msgid "Create book"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__create_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__create_uid
msgid "Created by"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__create_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__create_date
msgid "Created on"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__customer_id
msgid "Customer"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__label_color__dark
msgid "Dark"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__diagram_position
msgid "Diagram Position"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__display_name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__display_name
msgid "Display Name"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__bg_state__fail
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__label_state__fail
msgid "Fail"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__field_colorized_by_state_expression
msgid "Field Colorized By State Expression"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_m2o_info_widget_form
msgid "Field with M2o Info Widget (fields)"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_m2o_info_widget_form
msgid "Field with M2o Info Widget (method)"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_m2o_info_widget_form
msgid "Field without M2o Info Widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_tree_colored_field__field_colorized_by_state_expression
msgid "Field, colorized by expression"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_full_time_widget_form
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_full_time_widget_tree
msgid "Float Full Time(round_off: False)"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_full_time_widget_form
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_full_time_widget_tree
msgid "Float Full Time(round_off: True)"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_form
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_tree
msgid "Float Time Duration(round_off: False, time_only: False)"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_form
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_tree
msgid "Float Time Duration(round_off: False, time_only: True)"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_form
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_tree
msgid "Float Time Duration(round_off: True, time_only: False)"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_form
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_float_time_duration_widget_tree
msgid "Float Time Duration(round_off: True, time_only: True)"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.float_full_time_parent_menu_item
msgid "Float Time Widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.float_full_time_widget_menu_item
msgid "Float full time"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_float_full_time_widget_model
#: model:ir.actions.act_window,name:test_crnd_web_models.action_float_time_duration_widget_model
msgid "Float full time widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.float_time_duration_widget_menu_item
msgid "Float time duration"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__from_node_id
msgid "From"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_popover_widget_html_model__html_field_simple
msgid "HTML field no widgets."
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_popover_widget_html_model__html_popover_widget
msgid "HTML field with html popover widget."
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__html_field_simple
msgid "Html Field Simple"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__html_popover_widget
msgid "Html Popover Widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__id
msgid "ID"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__arrow_in_ids
msgid "Incoming Arrows"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__label_color
msgid "Label Color"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__label_state
msgid "Label State"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow____last_update
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node____last_update
msgid "Last Modified on"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__write_uid
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__write_uid
msgid "Last Updated by"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_brand__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__write_date
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__write_date
msgid "Last Updated on"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__bg_color__l_blue_bg
msgid "Light Blue"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__bg_color__l_brown_bg
msgid "Light Brown"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__bg_color__l_green_bg
msgid "Light Green"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__line_label_color
msgid "Line Label Color"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.m2o_info_widget_parent_menu_item
msgid "M2O Info Widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.m2o_info_widget_menu_item
msgid "M2O Info Widget Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_m2o_info_widget
msgid "M2o Info Widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_m2o_info_widget_model
msgid "Many2one widget Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.car_rental_model_action
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_car__model_id
msgid "Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_car_rental_model__name
msgid "Model Name"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_char_model__name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_html_model__name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_tree_colored_field__name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__name
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__name
msgid "Name"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus__node_ids
msgid "Nodes"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__bg_state__ok
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__label_state__ok
msgid "Ok"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__arrow_out_ids
msgid "Outgoing Arrows"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__partner_id
msgid "Partner"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_m2o_info_widget_form
msgid "Partner..."
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_char
msgid "Popover Widget Char"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_popover_widget_char_model
msgid "Popover Widget Char Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_html
msgid "Popover Widget HTML"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_popover_widget_html_model
msgid "Popover Widget HTML Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_text
msgid "Popover Widget Text"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_popover_widget_text_model
msgid "Popover Widget Text Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__res_label_color
msgid "Res Label Color"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_char_standard
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_html_standard
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_text_standard
msgid "Standard"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.crnd_web_actions_menu_item
#: model:ir.ui.menu,name:test_crnd_web_models.test_crnd_web_actions
msgid "Test crnd_web_actions"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.crnd_web_field_domain_menu_item
msgid "Test crnd_web_field_domain"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.crnd_web_on_create_action_menu_item
#: model:ir.ui.menu,name:test_crnd_web_models.test_crnd_web_on_create_action
msgid "Test crnd_web_on_create_action"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_test_crnd_web_actions
msgid "Test model for crnd_web_actions"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_test_crnd_web_model_book
msgid "Test model for crnd_web_on_create_action"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__text_field_simple
msgid "Text Field Simple"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_popover_widget_text_model__text_popover_widget
msgid "Text Popover Widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_popover_widget_text_model__text_field_simple
msgid "Text field no widgets."
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_popover_widget_text_model__text_popover_widget
msgid "Text field with text popover widget."
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_tree_colored_field__label_color
msgid "The field on the basis of which the label color of other fields"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_tree_colored_field__bg_color
msgid "The field on the basis of whichthe background color of other fields"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_tree_colored_field__bg_state
msgid ""
"This field defines background color of cell "
"\"field_colorized_by_state_expression\" field"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,help:test_crnd_web_models.field_tree_colored_field__label_state
msgid ""
"This field defines color of label \"field_colorized_by_state_expression\" "
"field"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__time_1
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__time_1
msgid "Time 1"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_full_time_widget__time_2
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__time_2
msgid "Time 2"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__time_3
msgid "Time 3"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_float_time_duration_widget__time_4
msgid "Time 4"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_book_wizard_create__title
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_actions__title
#: model:ir.model.fields,field_description:test_crnd_web_models.field_test_crnd_web_model_book__title
msgid "Title"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__to_node_id
msgid "To"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_tree_colored_field
#: model:ir.ui.menu,name:test_crnd_web_models.tree_colored_field_menu_item
#: model:ir.ui.menu,name:test_crnd_web_models.tree_colored_field_parent_menu_item
msgid "Tree Colored Field"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_tree_colored_field_model
msgid "Tree Colored Field Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_char_tree_editable
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_html_tree_editable
#: model:ir.ui.menu,name:test_crnd_web_models.popover_widget_text_tree_editable
msgid "Tree Editable"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_web_diagram_plus_form
msgid "View Diagram"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__bg_state__warning
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__label_state__warning
msgid "Warning"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_popover_widget_char_model
msgid "Web Char Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_popover_widget_char_model_editable_tree
msgid "Web Char Model Editable Tree"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_arrow__web_diagram_id
#: model:ir.model.fields,field_description:test_crnd_web_models.field_web_diagram_plus_node__web_diagram_id
msgid "Web Diagram"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_web_diagram_plus
#: model:ir.ui.menu,name:test_crnd_web_models.web_diagram_plus_menu_item
#: model:ir.ui.menu,name:test_crnd_web_models.web_diagram_plus_parent_menu_item
msgid "Web Diagram Plus"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_web_diagram_plus_arrow
msgid "Web Diagram Plus Arrow"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_web_diagram_plus_arrow_model
msgid "Web Diagram Plus Arrow Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.web_diagram_plus_arrow_menu_item
msgid "Web Diagram Plus Arrows"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_web_diagram_plus_model
msgid "Web Diagram Plus Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_web_diagram_plus_node
msgid "Web Diagram Plus Node"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_web_diagram_plus_node_model
msgid "Web Diagram Plus Node Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.ui.menu,name:test_crnd_web_models.web_diagram_plus_node_menu_item
msgid "Web Diagram Plus Nodes"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_popover_widget_html_model
msgid "Web HTML Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_popover_widget_html_model_editable_tree
msgid "Web HTML Model Editable Tree"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_popover_widget_text_model
msgid "Web Text Model"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.actions.act_window,name:test_crnd_web_models.action_popover_widget_text_model_editable_tree
msgid "Web Text Model Editable Tree"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields.selection,name:test_crnd_web_models.selection__tree_colored_field__label_color__white
msgid "White"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__widget_partner_id
msgid "Widget Partner"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model.fields,field_description:test_crnd_web_models.field_m2o_info_widget__widget_partner_method_id
msgid "Widget Partner Method"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.view_web_diagram_plus
msgid "['name']"
msgstr ""

#. module: test_crnd_web_models
#: model_terms:ir.ui.view,arch_db:test_crnd_web_models.test_car_rental_model_view_form
msgid "car_rental_model_form"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_float_full_time_widget
msgid "float.full.time.widget"
msgstr ""

#. module: test_crnd_web_models
#: model:ir.model,name:test_crnd_web_models.model_float_time_duration_widget
msgid "float.time.duration.widget"
msgstr ""
