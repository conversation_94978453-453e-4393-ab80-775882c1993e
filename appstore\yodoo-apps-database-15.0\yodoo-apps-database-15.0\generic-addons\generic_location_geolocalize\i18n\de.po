# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_location_geolocalize
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-03 16:07+0000\n"
"PO-Revision-Date: 2022-02-03 16:07+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__city
msgid "City"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__country_id
msgid "Country"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model,name:generic_location_geolocalize.model_generic_location_geolocalize_mixin
msgid "Generic Location Geolocalize Mixin"
msgstr ""

#. module: generic_location_geolocalize
#: model_terms:ir.ui.view,arch_db:generic_location_geolocalize.generic_location_form_view
msgid "Geolocalize"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location__geolocation_json
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__geolocation_json
msgid "Geolocation Json"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__latitude
msgid "Latitude"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model,name:generic_location_geolocalize.model_generic_location
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__location_id
msgid "Location"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__longitude
msgid "Longitude"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__state_id
msgid "State"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__street
msgid "Street"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__street2
msgid "Street2"
msgstr ""

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__zip
msgid "Zip"
msgstr ""
