Generic Crypto Utils
====================

.. |badge1| image:: https://img.shields.io/badge/pipeline-pass-brightgreen.png
    :target: https://github.com/crnd-inc/generic-addons

.. |badge2| image:: https://img.shields.io/badge/license-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3

.. |badge3| image:: https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png
    :target: https://yodoo.systems
    
.. |badge5| image:: https://img.shields.io/badge/maintainer-CR&D-purple.png
    :target: https://crnd.pro/
    


|badge1| |badge2| |badge5|

Generic Crypto Utils is technical addon developed by the `Center of Research &
Development company <https://crnd.pro/>`__. 

The goal of this addon is to provide generic utilities to add encryption to other addons.
Currently it implements ``generic.crypto.param`` model, wich works same
as ``ir.config_parameter`` but values stored are encrypted.

It is required to place *encryption key* in odoo configuration file (``odoo.conf``).
To do this, add following line to config file: ``crypto_token = <key>``

Run following command to generate new key:
``python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key())"``

This module allows you to easily encrypt fields in ``res.config.settings``.
To do this, all you need is to set attribute ``config_param=param_name`` to
such field, and this field will be encrypted. For example:

    .. code:: python

        class ResConfigSettings(models.TransientModel):
            _inherit = 'res.config.settings'

            my_secret_field = fields.Char(crypto_param='my.secret')

After this, you can access this param inside your python code like this:

    .. code:: python

        secret = self.env['generic.crypto.param'].get_param('my.secret')


Launch your own ITSM system in 60 seconds:
''''''''''''''''''''''''''''''''''''''''''

Create your own `Bureaucrat ITSM <https://yodoo.systems/saas/template/bureaucrat-itsm-demo-data-95>`__ database

|badge3| 


Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/crnd-inc/generic-addons/issues>`_.
In case of trouble, please check there if your issue has already been reported.


Maintainer
''''''''''
.. image:: https://crnd.pro/web/image/3699/300x140/crnd.png

Our web site: https://crnd.pro/

This module is maintained by the Center of Research & Development company.

We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you. 

For any questions `contact us <mailto:<EMAIL>>`__.





