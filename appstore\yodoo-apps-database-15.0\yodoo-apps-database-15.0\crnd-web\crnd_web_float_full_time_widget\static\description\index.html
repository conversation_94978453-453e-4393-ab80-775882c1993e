<section style="max-width: 896px; margin: 0 auto;">
    <div align="center">
        <h1 style="color:#875A7B;">CRnD Full Float Time Widget</h1>
    </div>
    <div align="center">
        <p><a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png"/></a> <a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://img.shields.io/badge/maintainer-CR&amp;D-purple.png"/></a> <a href="https://github.com/crnd-inc/crnd-web/tree/11.0/crnd_web_float_full_time_widget"><img alt="GitHub" src="https://img.shields.io/badge/GitHub-CRnD_Web_Full_Float_Time_Widget-green.png"/></a></p>
        <p><br></p>
    </div>
</section>

<section style="margin: 0 auto; max-width: 896px;">
    <div>
        <p>CRnD Full Float Time Widget is a module developed by the <a href="https://crnd.pro/">Center of Research &amp; Development</a> company.</p>
        <p>Widget consists of two parts: <i>Float Time Duration</i> and <i>Float Full Time</i>.</p>
        <ol>
            <li>
                <p><strong>Float Time Duration</strong></p>
                <p>Widget implies: the integer part of a float represents seconds, the fractional part - milliseconds.<br>
                For example: 94225.22 => 94225 seconds and 22 milliseconds.<br>
                Full template is: 0d 00:00:00.000 where Dd hh:mm:ss,msc are:<br>
                D - days, d - literal (days, can be translated), h - hours, m - minutes, s - seconds, msc - milliseconds.<br>
                For example: 1d 02:10:25,220 to float 94225.22.</p>
                <p>Widget has the following options:<br>
                <ul>
                    <li>
                        round_off - true, display template without milliseconds (false by default),
                    </li>
                    <li>
                        time_only - true, display template without days (false by default).
                    </li>
                </ul>
                </p>
                <p>For example:<br>
                <ul>
                    <li>
                        round_off: false, time_only: false:<br>
                        edit mode: 0d 00:00:00.000 (94225.22 to template 1d 02:10:25,220, 44439.999 to template 0d 12:20:39,999)<br>
                        normal mode: 0d 00:00:00.000 (94225.22 to template 1d 02:10:25,220, 44439.999 to template 12:20:39,999)<br>
                    </li>
                    <li>
                        round_off: true, time_only: false: 0d 00:00:00 (94225.22 to template 1d 02:10:25)<br>
                        edit mode: 0d 00:00:00 (94225.22 to template 1d 02:10:25, 44439.999 to template 0d 12:20:39)<br>
                        normal mode: 0d 00:00:00 (94225.22 to template 1d 02:10:25, 44439.999 to template 12:20:39)<br>
                    </li>
                    <li>
                        round_off: true, time_only: true: 00:00:00 (94225.22 to template 26:10:25)
                    </li>
                    <li>
                        round_off: false, time_only: true: 00:00:00,000 (94225.22 to template 26:10:25,220)
                    </li>
                </ul>
                </p>
                <p>
                    It simplifies operations with time.
                </p>
            </li>
            <li>
                <p><strong>Float Full Time</strong></p>
                <p>Widget based on the Float Time Duration widget.<br>
                Represents a float as a twenty-four hours time.<br>
                Widget restricts inappropriate input.<br>
                Data can be from 00:00:00,000 to 23:59:59,999 and only positive value.<br>
                It has the same options, but time_only always is true (except days).</p>
                <p>It can be used to make the start or stop time of any process.</p>
                <p>It means that it will contain the number of seconds from the start of the day.</p>
                <p>For example:<br>
                <ul>
                    <li>
                        00:00:00,000 in float 0 (midnight)
                    </li>
                    <li>
                        start_at = 01:22:30,220 (in float 4950.22 seconds from midnight)
                    </li>
                    <li>
                        stop_at = 04:45:15,560 (in float 17115.56 seconds from midnight)
                    </li>
                </ul>
                </p>
                <p>
                    It simplifies operations with time.
                </p>
            </li>
        </ol>
        <p style="font-size: 120%;">
            <i><b>How it works:</b></i></p>
        <p>Float Time Duration</p>
        <ul>
            <li>Define the 'float' field in the model:
                <pre><code>duration = fields.Float()</code></pre>
            </li>
            <li>Define the Float Time Duration widget on the form or tree view:
                <pre><code>&lt;group&gt;
    &lt;field name="duration" widget="float_time_duration" options="{'round_off': True, 'time_only': True}"/&gt;
&lt;/group&gt;
                </code></pre>
            </li>
        </ul>
        <p>Float Full Time</p>
        <ul>
            <li>Define the 'float' field in the model:
                <pre><code>duration = fields.Float()</code></pre>
            </li>
            <li>Define the Float Time Duration widget on the form or tree view:
                <pre><code>&lt;group&gt;
    &lt;field name="start_at" widget="float_full_time" options="{'round_off': True}"/&gt;
&lt;/group&gt;
                </code></pre>
            </li>
        </ul>
        <p style="font-size: 120%;">
            <i><b>As a result:</b></i></p>
        <ul>
            <li>
                <p style="font-size: 110%;">Float Time Duration</p>
                <div align="center">
                    <img src="float_time_duration.png" alt="Widget Float Time Duration on form" style="max-width: 100%">
                    <p><br></p>
                </div>
            </li>
            <li>
                <p style="font-size: 110%;">Float Full Time</p>
                <div align="center">
                    <img src="float_full_time.png" alt="Widget Float Full Time on form" style="max-width: 100%">
                    <p><br></p>
                </div>
                <div align="center">
                    <img src="float_full_time_error_1.png" alt="Widget Float Full Time on form" style="max-width: 100%">
                    <p><br></p>
                </div>
                <div align="center">
                    <img src="float_full_time_error_2.png" alt="Widget Float Full Time on form" style="max-width: 100%">
                    <p><br></p>
                </div>
            </li>
        </ul>
    </div>
</section>

<section style="max-width: 896px; margin: 0 auto;">
    <div>
        <h3>Bug Tracker</h3>
        <p>Bugs are tracked on <a href="https://crnd.pro/requests">https://crnd.pro/requests</a>. In case of trouble,
            please report there.
        </p>
        <p><br></p>
    </div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
    <div>
        <h2 style="color:#875A7B;">Launch your own ITSM system in 60 seconds:</h2>
        <p>Create your own <a href="https://yodoo.systems/saas/template/">Bureaucrat ITSM</a> database <a href="https://yodoo.systems"><img alt="yodoo.systems" src="https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png"/></a>
        </p>
    </div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
    <div>
        <h2 style="color: #138c33">Maintainer</h2>
        <a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://crnd.pro/web/image/3699/300x140/crnd.png"/></a>
        <p>Our web site: <a href="https://crnd.pro">https://crnd.pro</a>
        </p>
        <p>This module is maintained by the <a href="https://crnd.pro/">Center of Research &amp; Development</a>
            company.
        </p>
        <p>
            We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development
            and integration software, consulting services. Our main goal is to provide the best quality product for you.
        </p>
        <p>
            For any questions <a href="mailto:<EMAIL>">contact us</a>.
        </p>
    </div>
</section>