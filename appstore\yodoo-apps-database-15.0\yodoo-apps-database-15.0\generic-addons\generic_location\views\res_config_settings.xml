<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="generic_location_action_location_settings" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'generic_location'}</field>
    </record>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="model">res.config.settings</field>
        <field name="priority" eval="21"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div data-string="Generic Locations"
                     class="app_settings_block"
                     string="Generic Locations"
                     data-key="generic_location">

                    <h2>Generic location settings</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-xs-12 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="group_use_hierarchical_locations"
                                       string="Use hierarchical locations"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="group_use_hierarchical_locations"/>
                                <div class="text-muted">
                                    Enable this checkbox to build hierarchical location structures.
                                    When enable, you will be able to set parent location for any location,
                                    and see sub-locations of location.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <menuitem name="Settings"
              id="menu_settings_generic_location"
              parent="menu_configuration"
              action="generic_location_action_location_settings"
              sequence="1"/>
</odoo>
