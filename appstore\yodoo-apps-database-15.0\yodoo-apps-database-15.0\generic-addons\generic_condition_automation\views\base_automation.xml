<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_base_automation_form" model="ir.ui.view">
        <field name="name">base.automation.form</field>
        <field name="model">base.automation</field>
        <field name="inherit_id" ref="base_automation.view_base_automation_form"/>
        <field name="arch" type="xml">
            <field name="trigger" position="after">
                <field name="pre_condition_ids"
                       domain="[('model_id', '=', model_id)]"
                       class="oe_inline"
                       widget="many2many_tags"
                       context="{'default_model_id': model_id}"
                       options="{'no_quick_create': true, 'open': true}"
                       string="Set search conditions:"
                       attrs="{'invisible':[('trigger', 'not in', ['on_create_or_write', 'on_write'])]}"/>
            </field>
            <field name="filter_domain" position="before">
                <field name="post_condition_ids"
                       domain="[('model_id', '=', model_id)]"
                       class="oe_inline"
                       widget="many2many_tags"
                       context="{'default_model_id': model_id}"
                       options="{'no_quick_create': true, 'open': true}"
                       string="Set filter conditions:"
                       attrs="{'invisible':[('trigger', 'not in', ['on_create', 'on_create_or_write', 'on_write'])]}"/>
            </field>
        </field>
    </record>
</odoo>
