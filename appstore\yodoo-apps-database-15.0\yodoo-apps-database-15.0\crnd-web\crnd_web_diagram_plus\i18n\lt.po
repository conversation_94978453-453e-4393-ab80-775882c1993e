# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crnd_web_diagram_plus
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-26 11:14+0000\n"
"PO-Revision-Date: 2023-07-26 11:14+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: crnd_web_diagram_plus
#: model:ir.model,name:crnd_web_diagram_plus.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Veiksmo lango peržiūra"

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/js/diagram_controller.js:0
#, python-format
msgid "Activity"
msgstr "Veikla"

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/js/diagram_controller.js:0
#, python-format
msgid ""
"Are you sure you want to remove this node? This will remove its connected "
"transitions as well."
msgstr ""

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/js/diagram_controller.js:0
#, python-format
msgid "Are you sure you want to remove this transition?"
msgstr ""

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/xml/base_diagram.xml:0
#, python-format
msgid "Auto Layout"
msgstr ""

#. module: crnd_web_diagram_plus
#: code:addons/crnd_web_diagram_plus/tools/graph.py:0
#, python-format
msgid ""
"Cannot compute diagram view. It seems that the flow is incorrect! Try to "
"remove some broken routes without using diagram view."
msgstr ""

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/js/diagram_controller.js:0
#, python-format
msgid "Create:"
msgstr ""

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/js/diagram_view.js:0
#: model:ir.model.fields.selection,name:crnd_web_diagram_plus.selection__ir_actions_act_window_view__view_mode__diagram_plus
#: model:ir.model.fields.selection,name:crnd_web_diagram_plus.selection__ir_ui_view__type__diagram_plus
#, python-format
msgid "DiagramPlus"
msgstr ""

#. module: crnd_web_diagram_plus
#: code:addons/crnd_web_diagram_plus/models/ir_ui_view.py:0
#, python-format
msgid ""
"Field d_position_field must be present in diagram_plus[node], because set "
"auto_layout='False'"
msgstr ""

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/xml/base_diagram.xml:0
#, python-format
msgid "New Node"
msgstr ""

#. module: crnd_web_diagram_plus
#: code:addons/crnd_web_diagram_plus/models/ir_ui_view.py:0
#, python-format
msgid ""
"Only 'node' and 'arrow' tags allowed in 'diagram_plus_view', but "
"%(tag_name)s found."
msgstr ""

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/js/diagram_controller.js:0
#, python-format
msgid "Open:"
msgstr ""

#. module: crnd_web_diagram_plus
#. openerp-web
#: code:addons/crnd_web_diagram_plus/static/src/js/diagram_controller.js:0
#, python-format
msgid "Transition"
msgstr "Perėjimas"

#. module: crnd_web_diagram_plus
#: model:ir.model,name:crnd_web_diagram_plus.model_ir_ui_view
msgid "View"
msgstr "Rodinys"

#. module: crnd_web_diagram_plus
#: model:ir.model.fields,field_description:crnd_web_diagram_plus.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:crnd_web_diagram_plus.field_ir_ui_view__type
msgid "View Type"
msgstr "Rodinio tipas"
