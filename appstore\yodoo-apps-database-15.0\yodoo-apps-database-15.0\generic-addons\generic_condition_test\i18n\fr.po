# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_condition_test
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-20 12:36+0000\n"
"PO-Revision-Date: 2020-10-20 12:36+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_crm_partner_survey_sent_year_ago
msgid "CRM: Last survey (Feedback) from partner sent year ago"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_calendar_event_active
msgid "Calendar event: Active"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,description:generic_condition_test.test_condition_meeting_started_year_ago
msgid "Check that this meeting is started more then 1 year ago"
msgstr ""

#. module: generic_condition_test
#: model:res.groups,name:generic_condition_test.group_condition_no_access
msgid "Condition user with no access"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_contact_has_partners_active_calendar_events
msgid "Contact: has partners with active events"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_contact_has_lead_partner
msgid "Contact: has partners with leads"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__create_uid
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation__create_uid
msgid "Created by"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__create_date
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation__create_date
msgid "Created on"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__date_end
msgid "Date End"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__date_start
msgid "Date Start"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__date_test
msgid "Date Test"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__datetime_test
msgid "Datetime Test"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__display_name
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation__display_name
msgid "Display Name"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__id
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation__id
msgid "ID"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model____last_update
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation____last_update
msgid "Last Modified on"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__write_uid
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation__write_uid
msgid "Last Updated by"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__write_date
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation__write_date
msgid "Last Updated on"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_lead_partner_set
msgid "Lead: Partner set"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_meeting_started_year_ago
msgid "Meeting: Started Year ago"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_monetary_equal_150_usd
msgid "Monetary field equal 150 USD"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model_relation__name
msgid "Name"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_last_partner_meeting_1_year_ago
msgid "Partner: Last partner meeting started more then 1 year ago"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_last_partner_meeting_1_year_ago_city_or_lviv
msgid ""
"Partner: Last partner meeting started more then 1 year ago (in partner's "
"city or in Lviv)"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_last_partner_meeting_1_year_ago_city
msgid ""
"Partner: Last partner meeting started more then 1 year ago (in partner's "
"city)"
msgstr ""

#. module: generic_condition_test
#: model:generic.condition,name:generic_condition_test.test_condition_survey_sent_year_ago
msgid "Survey: sent year ago"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_bool
msgid "Test Bool"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_char
msgid "Test Char"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_float
msgid "Test Float"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_html
msgid "Test Html"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_int
msgid "Test Int"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_m2m
msgid "Test M2M"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_m2o
msgid "Test M2O"
msgstr ""

#. module: generic_condition_test
#: model:ir.model,name:generic_condition_test.model_test_generic_condition_test_model_relation
msgid "Test Model Relation for Generic Condition"
msgstr ""

#. module: generic_condition_test
#: model:ir.model,name:generic_condition_test.model_test_generic_condition_test_model
msgid "Test Model for Generic Condition"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_monetary
msgid "Test Monetary"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_monetary_currency
msgid "Test Monetary Currency"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_selection
msgid "Test Selection"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__test_text
msgid "Test Text"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__user_m2m
msgid "User M2M"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields,field_description:generic_condition_test.field_test_generic_condition_test_model__user_m2o
msgid "User M2O"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields.selection,name:generic_condition_test.selection__test_generic_condition_test_model__test_selection__val1
msgid "Value 1"
msgstr ""

#. module: generic_condition_test
#: model:ir.model.fields.selection,name:generic_condition_test.selection__test_generic_condition_test_model__test_selection__val2
msgid "Value 2"
msgstr ""
