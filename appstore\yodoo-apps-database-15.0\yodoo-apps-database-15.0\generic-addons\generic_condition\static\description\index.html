<section style="max-width: 896px; margin: 0 auto;">
<div align="center">
<h1 style="color:#875A7B;">Generic Condition</h1>
</div>
<div align="center">
<p><a href="https://github.com/crnd-inc/generic-addons"><img alt="pipeline-pass" src="https://img.shields.io/badge/pipeline-pass-brightgreen.png" /></a> <a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png" /></a> <a href="https://crnd.pro/doc-bureaucrat-itsm/11.0/en/Generic_Condition_admin_eng"><img alt="docs" src="https://img.shields.io/badge/docs-module-yellowgreen.png" /></a> <a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://img.shields.io/badge/maintainer-CR&amp;D-purple.png" /></a> <a href="https://github.com/crnd-inc/generic-addons/tree/11.0/generic_condition"><img alt="GitHub" src="https://img.shields.io/badge/GitHub-Generic_Condition-green.png"/></a></p>
<p><br></p>
</div>
</section>

<section style="margin: 0 auto; max-width: 896px;">
<div>
<p>Generic Condition is technical addon developed by the <a href="https://crnd.pro/">Center of Research &amp; Development</a> company.</p>
<p>This addon can be used by other developers in cases where they need to provide users with the ability to program some logic.</p>
<p>Generic Condition module can be used in other Odoo applications with the help of additional connection modules.</p>
<p>For example, conditions (that you program with Generic Condition addon) can be used to configure Automated Actions in the Generic Condition (automation) addon.</p>
</div>

<div>
<h3>Here are some basic types of conditions that can be customized:
</h3>
<ul>
<li>You can describe conditions as an expression in the Python
   programming language.
</li>
<li>Specify filters for selecting from DB.
</li>
<li>Use conditions with inversion or a group of conditions.
</li>
<li>Evaluate dates and monetary fields.
</li>
<li>Check objects and documents by users or specific fields.
</li>
</ul>
</div>
</section>

<section style="max-width: 896px; margin: 0 auto;">
<p>Read the <a href="https://crnd.pro/doc-bureaucrat-itsm/11.0/en/Generic_Condition_admin_eng/">Generic Condition</a> module guide for more information.</p>
<p>The Generic Condition module is part of the Bureaucrat ITSM project. You can try it by the links below.</p>
</section>

<section style="max-width: 896px; margin: 0 auto;">
<div>
<h3>Bug Tracker</h3>
<p>Bugs are tracked on <a href="https://github.com/crnd-inc/generic-addons/issues">GitHub Issues</a>. In case of trouble, please check there if your issue has already been reported.
</p>
<p><br></p>
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color:#875A7B;">Launch your own ITSM system in 60 seconds:</h2>
<p>Create your own <a href="https://yodoo.systems/saas/template/">Bureaucrat ITSM</a> database
<a href="https://yodoo.systems"><img alt="yodoo.systems" src="https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png"/></a>
</p> 
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color: #138c33">Maintainer</h2>
<a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://crnd.pro/web/image/3699/300x140/crnd.png" /></a>
<p>Our web site: <a href="https://crnd.pro">https://crnd.pro</a>
</p>
<p>This module is maintained by the <a href="https://crnd.pro/">Center of Research &amp; Development</a>
company.
</p>
<p>
We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you. 
</p>
<p>
For any questions <a href="mailto:<EMAIL>">contact us</a>.
</p>
</div>
</section>
