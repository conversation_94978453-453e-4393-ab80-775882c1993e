{
    "name": "Base Field Many2many View",
    "version": "********.0",
    "author": "Center of Research and Development",
    "website": "https://crnd.pro",
    "license": "LGPL-3",
    "summary": (
        "Adds Many2manyView field implementation for Odoo. "
        "Useful in cases when m2m relation computed via Postgresql View"),
    'category': 'Technical Settings',
    'depends': [
        "base",
    ],
    'demo': [
    ],
    'data': [
    ],
    'images': ['static/description/banner.png'],
    'installable': True,
    'auto_install': False,
}
