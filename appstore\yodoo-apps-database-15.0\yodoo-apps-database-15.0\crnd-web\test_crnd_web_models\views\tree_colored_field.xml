<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_tree_colored_field_tree" model="ir.ui.view">
        <field name="model">tree.colored.field</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"
                       options="{'field_bg_color': 'line_bg_color', 'field_label_color': 'line_label_color'}"/>
                <field name="line_bg_color" invisible="1"/>
                <field name="line_label_color" invisible="1"/>
                <field name="bg_color"/>
                <field name="label_color"/>
                <field name="field_colorized_by_state_expression"
                       options="{&quot;field_label_color_expression&quot;: &quot;
                       #4B0082:label_state == 'fail';
                       #2F4F4F:label_state == 'warning';
                       #00008B:label_state == 'ok'&quot;,
                                &quot;field_bg_color_expression&quot;: &quot;
                       #E6E6FA:bg_state == 'fail';
                       #98FB98:bg_state == 'warning';
                       #D3D3D3:bg_state == 'ok'&quot;}"/>
                <field name="label_state"/>
                <field name="bg_state"/>
            </tree>
        </field>
    </record>

    <record id="view_tree_colored_field_form" model="ir.ui.view">
        <field name="model">tree.colored.field</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="bg_color"/>
                        <field name="label_color"/>
                    </group>
                    <group>
                        <field name="field_colorized_by_state_expression"/>
                        <field name="label_state"/>
                        <field name="bg_state"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_tree_colored_field_model" model="ir.actions.act_window">
        <field name="name">Tree Colored Field Model</field>
        <field name="res_model">tree.colored.field</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem name="Tree Colored Field"
              id="tree_colored_field_parent_menu_item"
              sequence="210"
              parent="crnd_web_tests"/>

    <menuitem name="Tree Colored Field"
              id="tree_colored_field_menu_item"
              sequence="10"
              parent="tree_colored_field_parent_menu_item"
              action="action_tree_colored_field_model"/>

</odoo>
