<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_move_tags_view_action" model="ir.actions.act_window">
        <field name="name">Invoice Tags</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">generic.tag</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'default_model': 'account.move'}</field>
        <field name="domain">[('model_id.model','=','account.move')]</field>
    </record>

    <menuitem id="menu_account_tags"
              parent="generic_tag.menu_generic_tags_root"
              name="Account Tags"
              sequence="20"/>
    <menuitem id="menu_account_move_tags_id" name="Invoice Tags"
              parent="menu_account_tags" sequence="10"
              action="account_move_tags_view_action"/>
</odoo>
