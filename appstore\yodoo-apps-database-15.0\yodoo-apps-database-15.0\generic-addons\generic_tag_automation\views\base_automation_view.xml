<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Action Rule Form View -->
    <record id="view_base_automation_form" model="ir.ui.view">
        <field name="name">base.automation.form.tags</field>
        <field name="model">base.automation</field>
        <field name="inherit_id" ref="base_automation.view_base_automation_form"/>
        <field name="arch" type="xml">
            <field name="state" position="after">
                <field name="act_add_tag_ids"
                       options="{'color_field': 'color'}"
                       widget="many2many_tags"
                       attrs="{'invisible':[('state','!=','set_tag')]}"
                       context="{'default_model': model_name}"/>
                <field name="act_remove_tag_ids"
                       options="{'color_field': 'color'}"
                       widget="many2many_tags"
                       attrs="{'invisible':[('state','!=','set_tag')]}"
                       context="{'default_model': model_name}"/>
            </field>
        </field>
    </record>
</odoo>

