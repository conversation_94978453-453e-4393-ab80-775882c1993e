<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="move_search_form_view" model="ir.ui.view">
        <field name="name">account.move.search.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_move_filter"/>
        <field name="arch" type="xml">
            <field name="journal_id" position="after">
                <field name="tag_ids"
                       options="{'color_field': 'color'}"/>
                <field name="search_tag_id"/>
                <field name="search_no_tag_id"/>
            </field>
        </field>
    </record>

    <record id="account_invoice_tree_view" model="ir.ui.view">
        <field name="name">account.move.tags.tree</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_invoice_tree"/>
        <field name="arch" type="xml">
            <field name="state" position="after">
                <field name="tag_ids"
                       options="{'color_field': 'color'}"
                       widget="many2many_tags"
                       placeholder="Tags..."/>
            </field>
        </field>
    </record>

    <record id="view_account_move_tag_form" model="ir.ui.view">
        <field name="name">account.move.tag.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form//sheet/div/h1" position="after">
                <field name="tag_ids"
                        options="{'color_field': 'color'}"
                        widget="many2many_tags"
                        placeholder="Tags..."
                        context="{'default_model': 'account.move'}"/>
            </xpath>
        </field>
    </record>
</odoo>
