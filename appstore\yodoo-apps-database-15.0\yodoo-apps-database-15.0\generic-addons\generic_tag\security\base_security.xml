<?xml version="1.0"?>
<odoo>
    <!-- Look at Odoo code (openepr|odoo)/modules/db.py:create_categories
         for info abuto a way module categories are generated.
         This xmlid is used to avoid duplication of module category -->
    <record model="ir.module.category" id="base.module_category_generic_tags">
        <field name="name">Generic Tags</field>
        <field name="sequence">100</field>
        <field name="visible" eval="1" />
    </record>

    <!--
        Users Groups
    -->
    <record model="res.groups" id="group_tags_user_restricted">
        <field name="name">Tag User (restricted)</field>
        <field name="category_id" ref="base.module_category_generic_tags"/>
        <field name="comment">
            Users in this group have access to tags that have same group that user have
        </field>
    </record>
    <record model="res.groups" id="group_tags_user">
        <field name="name">Tag User</field>
        <field name="category_id" ref="base.module_category_generic_tags"/>
        <field name="comment">
            Users in this group have access to menu Tags. They cannot create or edit tags.
        </field>
    </record>
    <record model="res.groups" id="group_tags_manager">
        <field name="name">Tag Manager</field>
        <field name="implied_ids" eval="[(4, ref('generic_tag.group_tags_user'))]"/>
        <field name="category_id" ref="base.module_category_generic_tags"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        <field name="comment">
            Users in this group can manage tags (create / update / ...)
        </field>
    </record>

    <!-- Global tag rules -->
    <record id="rule_generic_tags_user" model="ir.rule">
        <field name="name">Global: see tags w/o group or if user in same group</field>
        <field name="model_id" ref="generic_tag.model_generic_tag"/>
        <field name="domain_force">['|', '|', ('group_ids','=',False),('group_ids.users.id','=',user.id), (int(user.has_group('generic_tag.group_tags_manager')), '=', 1)]</field>
    </record>
    <record id="rule_generic_tags_user_restricted" model="ir.rule">
        <field name="name">Restricted tag user</field>
        <field name="model_id" ref="generic_tag.model_generic_tag"/>
        <field name="groups" eval="[(4, ref('generic_tag.group_tags_user_restricted'))]"/>
        <field name="domain_force">[('group_ids.users.id','=',user.id)]</field>
    </record>
    <record id="rule_generic_tags_manager" model="ir.rule">
        <field name="name">Manager can see all tags</field>
        <field name="model_id" ref="generic_tag.model_generic_tag"/>
        <field name="groups" eval="[(4, ref('generic_tag.group_tags_manager'))]"/>
        <field name="domain_force">[(1, '=', 1)]</field>
    </record>
</odoo>

