<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="example_wizard_create_form_view" model="ir.ui.view">
        <field name="name">book.wizard.create.form</field>
        <field name="model">book.wizard.create</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="title"/>
                    </group>
                </sheet>
                <footer>
                    <button name="create_book"
                            string="Create book"
                            type="object"
                            class="btn-primary"/>
                    <button string="Cancel"
                            class="btn-default"
                            special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record id="action_wizard_book_create" model="ir.actions.act_window">
       <field name="res_model">book.wizard.create</field>
       <field name="name">Create book</field>
       <field name="view_mode">form</field>
       <field name="target">new</field>
   </record>
</odoo>
