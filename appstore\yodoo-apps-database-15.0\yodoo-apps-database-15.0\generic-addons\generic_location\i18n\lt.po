# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_location
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-26 11:13+0000\n"
"PO-Revision-Date: 2023-07-26 11:13+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid ""
"<span class=\"oe_inline\"> ( </span>\n"
"                                        <span> Lat : </span>"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "<span> : Long : </span>"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "<span>) </span>"
msgstr ""

#. module: generic_location
#: model:res.groups,comment:generic_location.group_generic_location_user
msgid "Access generic locations"
msgstr ""

#. module: generic_location
#: model:res.groups,comment:generic_location.group_generic_location_user_implicit
msgid ""
"Access generic locations (no access to menu, access only to locations he is "
"subscribed to, and theirs sublocations)\n"
"        "
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_needaction
msgid "Action Needed"
msgstr "Reikalingas veiksmas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__active
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__active
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
msgid "Active"
msgstr "Aktyvus"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Address"
msgstr "Adresas"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Address Configuration"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__state_name
msgid ""
"Administrative divisions of a country. E.g. Fed. State, Departement, Canton"
msgstr ""
"Valstybės administracinis vienetas. Pvz. savivaldybė, valstija, kantonas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__child_all_ids
msgid "All Childs"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__location_count
msgid "All Locations"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_type_search_view
msgid "Archived"
msgstr "Archyvuota"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_attachment_count
msgid "Attachment Count"
msgstr "Prisegtukų skaičius"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__child_all_count
msgid "Child All Count"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__child_count
msgid "Child Count"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__city
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__city
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__city
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "City"
msgstr "Miestas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__code
msgid "Code"
msgstr "Kodas"

#. module: generic_location
#: model:ir.model,name:generic_location.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: generic_location
#: model:ir.ui.menu,name:generic_location.menu_configuration
msgid "Configuration"
msgstr "Konfigūracija"

#. module: generic_location
#: model:ir.model,name:generic_location.model_res_partner
msgid "Contact"
msgstr "Kontaktai"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__country_id
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__country_id
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__country_id
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
msgid "Country"
msgstr "Valstybė"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__country_name
msgid "Country Name"
msgstr "Valstybės pavadinimas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__create_uid
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__create_date
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: generic_location
#: model:generic.tag,name:generic_location.simple_tag_location_1
msgid "Darnitsa"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__description
msgid "Description"
msgstr "Aprašymas"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Description..."
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__display_name
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.res_config_settings_view_form
msgid ""
"Enable this checkbox to build hierarchical location structures.\n"
"                                    When enable, you will be able to set "
"parent location for any location,\n"
"                                    and see sub-locations of location."
msgstr ""

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__search_tag_id
msgid "Find all records that contain this tag"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__search_no_tag_id
msgid "Find all records that have no this tag"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_follower_ids
msgid "Followers"
msgstr "Sekėjai"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekėjai (partneriai)"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_res_partner__generic_location_ids
#: model:ir.model.fields,field_description:generic_location.field_res_users__generic_location_ids
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_typeform_view
msgid "Generic Location"
msgstr ""

#. module: generic_location
#: model:ir.model,name:generic_location.model_generic_location_address_mixin
msgid "Generic Location Address Mixin"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_res_partner__generic_location_count
#: model:ir.model.fields,field_description:generic_location.field_res_users__generic_location_count
msgid "Generic Location Count"
msgstr ""

#. module: generic_location
#: model:ir.model,name:generic_location.model_generic_location_mixin
msgid "Generic Location Mixin"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.res_config_settings_view_form
msgid "Generic Locations"
msgstr ""

#. module: generic_location
#: model:res.groups,name:generic_location.group_generic_location_manager
msgid "Generic location manager"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.res_config_settings_view_form
msgid "Generic location settings"
msgstr ""

#. module: generic_location
#: model:res.groups,name:generic_location.group_generic_location_user
msgid "Generic location user"
msgstr ""

#. module: generic_location
#: model:res.groups,name:generic_location.group_generic_location_user_implicit
msgid "Generic location user (implicit)"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Geolocation"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
msgid "Group by..."
msgstr "Grupuoti pagal..."

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__has_message
msgid "Has Message"
msgstr "Turi žinutę"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__id
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__id
msgid "ID"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__message_needaction
#: model:ir.model.fields,help:generic_location.field_generic_location__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeigu pažymėta, naujiems pranešimams reikės jūsų dėmesio."

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jei pažymėta, yra žinučių, turinčių pristatymo klaidų."

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__image_1920
msgid "Image"
msgstr "Paveikslėlis"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__image_1024
msgid "Image 1024"
msgstr "Paveikslėlis "

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__image_128
msgid "Image 128"
msgstr "Paveikslėlis 128"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__image_256
msgid "Image 256"
msgstr "Paveikslėlis 256"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__image_512
msgid "Image 512"
msgstr "Paveikslėlis 512"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Info"
msgstr "Informacija"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_is_follower
msgid "Is Follower"
msgstr "Yra sekėjas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location____last_update
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__write_uid
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__write_date
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__latitude
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__latitude
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__latitude
msgid "Latitude"
msgstr ""

#. module: generic_location
#: model:generic.tag.model,name:generic_location.generic_location_tag_model
#: model:ir.actions.act_window,name:generic_location.generic_location_action
#: model:ir.model,name:generic_location.model_generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__location_id
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__location_id
#: model:ir.ui.menu,name:generic_location.menu_generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_typeform_view
msgid "Location"
msgstr "Vieta"

#. module: generic_location
#: model:ir.actions.act_window,name:generic_location.action_generic_location_tags_view
#: model:ir.ui.menu,name:generic_location.menu_location_tags
#: model:ir.ui.menu,name:generic_location.menu_tags_location_tags
#: model:ir.ui.menu,name:generic_location.menu_tags_location_tags_action
msgid "Location Tags"
msgstr ""

#. module: generic_location
#: model:ir.actions.act_window,name:generic_location.generic_location_type_action
#: model:ir.model,name:generic_location.model_generic_location_type
#: model:ir.ui.menu,name:generic_location.menu_generic_location_type
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Location Type"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_typeform_view
msgid "Location type code..."
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__location_ids
#: model:ir.ui.menu,name:generic_location.menu_generic_location_root
#: model_terms:ir.ui.view,arch_db:generic_location.view_partner_form
msgid "Locations"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__longitude
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__longitude
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__longitude
msgid "Longitude"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pagrindinis prisegtukas"

#. module: generic_location
#: model:res.groups,comment:generic_location.group_generic_location_manager
msgid "Manage generic locations"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_has_error
msgid "Message Delivery error"
msgstr "Žinutės pristatymo klaida"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_ids
msgid "Messages"
msgstr "Žinutės"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__name
#: model:ir.model.fields,field_description:generic_location.field_generic_location_type__name
msgid "Name"
msgstr "Vardas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__search_no_tag_id
msgid "No tag"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_needaction_counter
msgid "Number of Actions"
msgstr "Veiksmų skaičius"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_has_error_counter
msgid "Number of errors"
msgstr "Klaidų kiekis"

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Žinučių, kurioms reikia jūsų veiksmo, skaičius"

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Žinučių su pristatymo klaida skaičius"

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__message_unread_counter
msgid "Number of unread messages"
msgstr "Neperskaitytų žinučių skaičius"

#. module: generic_location
#: model:generic.tag,name:generic_location.simple_tag_location_3
msgid "Osocorki"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__parent_id
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Parent Location"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__parent_path
msgid "Parent Path"
msgstr "Tėvinis kelias"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__parent_ids
msgid "Parents"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__partner_id
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
msgid "Partner"
msgstr "Partneris"

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__partner_id
msgid "Partner / customer related to this location."
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
msgid "Partner Locations"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Partner or customer..."
msgstr ""

#. module: generic_location
#: model:generic.tag,name:generic_location.simple_tag_location_2
msgid "Poznyaki"
msgstr ""

#. module: generic_location
#: model:generic.tag,name:generic_location.simple_tag_location_4
msgid "Rusanovka"
msgstr ""

#. module: generic_location
#: model:ir.actions.act_window,name:generic_location.generic_location_action_location_settings
#: model:ir.ui.menu,name:generic_location.menu_settings_generic_location
msgid "Settings"
msgstr "Nustatymai"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__state_id
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__state_id
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__state_id
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
msgid "State"
msgstr "Regionas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__state_name
msgid "State Name"
msgstr "Savivaldybės pavadinimas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__street
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__street
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__street
msgid "Street"
msgstr "Gatvė"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Street 2..."
msgstr "Gatvė 2..."

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Street..."
msgstr "Gatvė..."

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__street2
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__street2
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__street2
msgid "Street2"
msgstr "Gatvė 2"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "Sublocation"
msgstr ""

#. module: generic_location
#: code:addons/generic_location/models/generic_location.py:0
#: model:ir.model.fields,field_description:generic_location.field_generic_location__child_ids
#, python-format
msgid "Sublocations"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location___city
msgid "System City"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location___country_id
msgid "System Country"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location___state_id
msgid "System State"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location___street
msgid "System Street"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location___street2
msgid "System Street2"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location___zip
msgid "System Zip"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__search_tag_id
msgid "Tag"
msgstr "Tagas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__tag_ids
msgid "Tags"
msgstr "Žymos"

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_tree_view
msgid "Tags..."
msgstr "Žymos..."

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__country_name
msgid "The full name of the country."
msgstr "Pilnas valstybės pavadinimas"

#. module: generic_location
#: model:ir.model.constraint,message:generic_location.constraint_generic_location_name_description_check
msgid "The title of the Location should not be the description"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__type_id
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_search_view
msgid "Type"
msgstr "Tipas"

#. module: generic_location
#: model:ir.model.fields,help:generic_location.field_generic_location__type_id
msgid "Type of Location"
msgstr ""

#. module: generic_location
#: model:generic.location,country_name:generic_location.demo_location_city_us_ny_buffalo
#: model:generic.location,country_name:generic_location.demo_location_country_us
#: model:generic.location,country_name:generic_location.demo_location_state_us_ny
#: model:generic.location,country_name:generic_location.demo_location_street_us_ny_buffalo_4371_bottom_lane
#: model:generic.location,country_name:generic_location.simple_location_room_1
#: model:generic.location,country_name:generic_location.simple_location_room_10
#: model:generic.location,country_name:generic_location.simple_location_room_101
#: model:generic.location,country_name:generic_location.simple_location_room_11
#: model:generic.location,country_name:generic_location.simple_location_room_111
#: model:generic.location,country_name:generic_location.simple_location_room_12
#: model:generic.location,country_name:generic_location.simple_location_room_13
#: model:generic.location,country_name:generic_location.simple_location_room_14
#: model:generic.location,country_name:generic_location.simple_location_room_1_l1_b1
#: model:generic.location,country_name:generic_location.simple_location_room_1_l1_b2
#: model:generic.location,country_name:generic_location.simple_location_room_1_l2_b1_1
#: model:generic.location,country_name:generic_location.simple_location_room_1_l2_b1_2
#: model:generic.location,country_name:generic_location.simple_location_room_1_l2_b2_1
#: model:generic.location,country_name:generic_location.simple_location_room_1_l3_b1_1
#: model:generic.location,country_name:generic_location.simple_location_room_2
#: model:generic.location,country_name:generic_location.simple_location_room_21
#: model:generic.location,country_name:generic_location.simple_location_room_3
#: model:generic.location,country_name:generic_location.simple_location_room_4
#: model:generic.location,country_name:generic_location.simple_location_room_5
#: model:generic.location,country_name:generic_location.simple_location_room_51
#: model:generic.location,country_name:generic_location.simple_location_room_6
#: model:generic.location,country_name:generic_location.simple_location_room_61
#: model:generic.location,country_name:generic_location.simple_location_room_7
#: model:generic.location,country_name:generic_location.simple_location_room_71
#: model:generic.location,country_name:generic_location.simple_location_room_8
#: model:generic.location,country_name:generic_location.simple_location_room_81
#: model:generic.location,country_name:generic_location.simple_location_room_9
#: model:generic.location,country_name:generic_location.simple_location_room_91
#: model:generic.location,country_name:generic_location.simple_location_root_1
#: model:generic.location,country_name:generic_location.simple_parent_location_1
#: model:generic.location,country_name:generic_location.simple_parent_location_2
#: model:generic.location,country_name:generic_location.simple_parent_location_3
#: model:generic.location,country_name:generic_location.simple_parent_location_4
#: model:generic.location,country_name:generic_location.simple_parent_location_5
#: model:generic.location,country_name:generic_location.simple_parent_location_6
msgid "United States"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_unread
msgid "Unread Messages"
msgstr "Neperskaitytos žinutės"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Neperskaitytų žinučių skaičiavimas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__city_use_parent
msgid "Use Parent City"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__country_id_use_parent
msgid "Use Parent Country"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__state_id_use_parent
msgid "Use Parent State"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__street_use_parent
msgid "Use Parent Street"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__street2_use_parent
msgid "Use Parent Street2"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__zip_use_parent
msgid "Use Parent Zip"
msgstr ""

#. module: generic_location
#: model:res.groups,name:generic_location.group_use_hierarchical_locations
msgid "Use hierarchical location"
msgstr ""

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_res_config_settings__group_use_hierarchical_locations
#: model:res.groups,comment:generic_location.group_use_hierarchical_locations
#: model_terms:ir.ui.view,arch_db:generic_location.res_config_settings_view_form
msgid "Use hierarchical locations"
msgstr ""

#. module: generic_location
#: model_terms:ir.ui.view,arch_db:generic_location.generic_location_form_view
msgid "ZIP"
msgstr "Pašto kodas"

#. module: generic_location
#: model:ir.model.fields,field_description:generic_location.field_generic_location__zip
#: model:ir.model.fields,field_description:generic_location.field_generic_location_address_mixin__zip
#: model:ir.model.fields,field_description:generic_location.field_generic_location_mixin__zip
msgid "Zip"
msgstr "Pašto kodas"
