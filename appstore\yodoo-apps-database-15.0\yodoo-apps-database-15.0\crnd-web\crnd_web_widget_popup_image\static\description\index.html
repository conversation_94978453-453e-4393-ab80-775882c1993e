<section style="max-width: 896px; margin: 0 auto;">
<div align="center">
<h1 style="color:#875A7B;">CRnD Widget Popup Image</h1>
</div>
<div align="center">
<p><a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html"><img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png" /></a> <a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://img.shields.io/badge/maintainer-CR&amp;D-purple.png" /></a> <a href="https://github.com/crnd-inc/crnd-web/tree/11.0/crnd_web_widget_popup_image"><img alt="GitHub" src="https://img.shields.io/badge/GitHub-CRnD_Widget_Popup_Image-green.png"/></a></p>
<p><br></p>
</div>
</section>

<section style="margin: 0 auto; max-width: 896px;">
<div>
<p>CRnD Widget Popup Image is a module developed by the <a href="https://crnd.pro/">Center of Research &amp; Development</a> company. This widget allows you to popup images from the binary fields. It is available on the form and tree views.</p>
<p style="font-size: 120%;">
<i><b>How it works:</b></i></p>
<ul>
<li>Define a widget on the form view:
<pre><code>&lt;group&gt;
    &lt;field name="image_test" widget="image_popup" class="oe_avatar"/&gt;
&lt;/group&gt;
</code></pre>
</li>
<li>If necessary, define a widget on the tree view:
<pre><code>&lt;tree&gt;
    &lt;field name="test_field"/&gt;
    &lt;field name="image_test" widget="image_popup" class="ot_image"/&gt;
&lt;/tree&gt;
</code></pre>
<p>The <code>ot_image</code> CSS class resizes the image for the tree view (you can use your own class.)</p>
</li>
</ul>
<p style="font-size: 120%;">
<i><b>As a result:</b></i></p>
<ul>
<li>
<p style="font-size: 110%;">Form view</p>
<div align="center">
<img src="image_form_view.png" alt="Widget on form" style="max-width: 100%">
<p><br></p>
</div>
</li>
<li>
<p style="font-size: 110%;">Tree view</p>
<div align="center">
<img src="image_tree_view.png" alt="Widget on tree" style="max-width: 100%">
<p><br></p>
</div>
</li>
<li>
<p style="font-size: 110%;">Popup</p>
<div align="center">
<img src="image_popup.png" alt="Widget popup" style="max-width: 100%">
<p><br></p>
</div>
</li>
<li>
<p style="font-size: 110%;">Edit mode<br/></p>
<p>In the edit mode, it works as a standard image widget.</p>
<div align="center">
<img src="edit_mode_image.png" alt="Widget edit mode" style="max-width: 100%">
<p><br></p>
</div>
</li>
</ul>
</div>
</section>

<section style="max-width: 896px; margin: 0 auto;">
<div>
<h3>Bug Tracker</h3>
<p>Bugs are tracked on <a href="https://crnd.pro/requests">https://crnd.pro/requests</a>. In case of trouble, please report there.
</p>
<p><br></p>
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color:#875A7B;">Launch your own ITSM system in 60 seconds:</h2>
<p>Create your own <a href="https://yodoo.systems/saas/template/">Bureaucrat ITSM</a> database
<a href="https://yodoo.systems"><img alt="yodoo.systems" src="https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png"/></a>
</p> 
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color: #138c33">Maintainer</h2>
<a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://crnd.pro/web/image/3699/300x140/crnd.png" /></a>
<p>Our web site: <a href="https://crnd.pro">https://crnd.pro</a>
</p>
<p>This module is maintained by the <a href="https://crnd.pro/">Center of Research &amp; Development</a>
company.
</p>
<p>
We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you. 
</p>
<p>
For any questions <a href="mailto:<EMAIL>">contact us</a>.
</p>
</div>
</section>