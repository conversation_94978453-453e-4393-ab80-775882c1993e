# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_location_geolocalize
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 15:05+0000\n"
"PO-Revision-Date: 2023-07-26 09:29+0000\n"
"Last-Translator: Den_Sharaf <<EMAIL>>\n"
"Language-Team: Ukrainian <http://weblate.crnd.pro/projects/generic-"
"addons-15-0/generic_location_geolocalize/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__city
msgid "City"
msgstr "Місто"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__country_id
msgid "Country"
msgstr "Країна"

#. module: generic_location_geolocalize
#: model:ir.model,name:generic_location_geolocalize.model_generic_location_geolocalize_mixin
msgid "Generic Location Geolocalize Mixin"
msgstr ""

#. module: generic_location_geolocalize
#: model_terms:ir.ui.view,arch_db:generic_location_geolocalize.generic_location_form_view
msgid "Geolocalize"
msgstr "Геолокалізація"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location__geolocation_json
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__geolocation_json
msgid "Geolocation Json"
msgstr "Геолокація Json"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__latitude
msgid "Latitude"
msgstr "Широта"

#. module: generic_location_geolocalize
#: model:ir.model,name:generic_location_geolocalize.model_generic_location
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__location_id
msgid "Location"
msgstr "Місцезнаходження"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__longitude
msgid "Longitude"
msgstr "Довгота"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__state_id
msgid "State"
msgstr "Стан"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__street
msgid "Street"
msgstr "Вулиця"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__street2
msgid "Street2"
msgstr "Вулиця2"

#. module: generic_location_geolocalize
#: model:ir.model.fields,field_description:generic_location_geolocalize.field_generic_location_geolocalize_mixin__zip
msgid "Zip"
msgstr "Зіп Код"

#~ msgid "Select on the map"
#~ msgstr "Виберіть на карті"

#~ msgid "Display Name"
#~ msgstr "Назва для відображення"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Остання модифікація"
