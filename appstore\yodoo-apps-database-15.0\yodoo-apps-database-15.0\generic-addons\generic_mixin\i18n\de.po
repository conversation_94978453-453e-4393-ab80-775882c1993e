# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_mixin
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-20 12:36+0000\n"
"PO-Revision-Date: 2020-10-20 12:36+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__active
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__active
msgid "Active"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__archived
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__archived
msgid "Archived"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_no_unlink.py:0
#, python-format
msgid ""
"Cannot unlink these records. Operation not allowed.\n"
"It is better to deactivate these records.\n"
"Model: %(model)s [%(model_name)s]"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_no_unlink.py:0
#, python-format
msgid ""
"Cannot unlink these records. Operation not allowed.\n"
"Model: %(model)s [%(model_name)s]"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_name_with_code__code
msgid "Code"
msgstr ""

#. module: generic_mixin
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_location_type_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_mixin_name_with_code_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_resource_type_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_group_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_level_code_ascii_only
msgid "Code must be ascii only"
msgstr ""

#. module: generic_mixin
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_location_type_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_mixin_uniq_name_code_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_resource_type_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_group_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_level_code_uniq
msgid "Code must be unique."
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_id
msgid "Data record"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_ids
msgid "Data records"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_guard_fields.py:0
#, python-format
msgid ""
"Direct modification of '%(model)s:%(field_name)s' field is is not allowed!"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__draft
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__draft
msgid "Draft"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__email
msgid "Email"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_parent.py:0
#, python-format
msgid "Error ! You cannot create recursive %s."
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_delegation_interface.py:0
#, python-format
msgid "Error: unknown implementation"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_delegation_implementation
msgid "Generic Mixin Delegation: Implementation"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_delegation_interface
msgid "Generic Mixin Delegation: Interface"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_contact
msgid "Generic Mixin: Contacts"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_entity_lifecycle
msgid "Generic Mixin: Entity Lifecycle"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_get_action
msgid "Generic Mixin: Get Action"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_guard_fields
msgid "Generic Mixin: Guard Fields"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_name_by_sequence
msgid "Generic Mixin: Name by Sequence"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_namesearch_by_fields
msgid "Generic Mixin: Name search by fields"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_name_with_code
msgid "Generic Mixin: Name with code"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_no_unlink
msgid "Generic Mixin: No Unlink"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_parent_names
msgid "Generic Mixin: Parent Names"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_proxy_methods
msgid "Generic Mixin: Proxy Methods"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_refresh_view
msgid "Generic Mixin: Refresh view"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_track_changes
msgid "Generic Mixin: Track Changes"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_uuid
msgid "Generic Mixin: UUID"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_uniq_name_code
msgid "Generic Mixin: Unique name and code"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_data_updatable
msgid "GenericMixin: Data Updatable"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_transaction_utils
msgid "GenericMixin: Transaction Utils"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,help:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_no_update
msgid ""
"Indicates whether this record will be updated with module update or not. If "
"set to True, the record will not be overriden on module update, if set to "
"False, thenrecord will be overridden by module update"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_contact.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_entity_lifecycle.py:0
#, python-format
msgid ""
"It is not allowed to change Lifecycle State fieldfrom %(old_state)s to "
"%(new_state)s!\n"
"Allowed next states: %(allowed_states)s"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_activated
msgid "Lifecycle Date Activated"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_archived
msgid "Lifecycle Date Archived"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_created
msgid "Lifecycle Date Created"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_obsolete
msgid "Lifecycle Date Obsolete"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_state
msgid "Lifecycle State"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_fb
msgid "Link Fb"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_instagram
msgid "Link Instagram"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_linkedin
msgid "Link Linkedin"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_telegram
msgid "Link Telegram"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_twitter
msgid "Link Twitter"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_viber
msgid "Link Viber"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_whatsapp
msgid "Link Whatsapp"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_youtube
msgid "Link Youtube"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_city
msgid "Location City"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_country_id
msgid "Location Country"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_state_id
msgid "Location State"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_street
msgid "Location Street"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_street2
msgid "Location Street2"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_zip
msgid "Location Zip"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_name_with_code__name
msgid "Name"
msgstr ""

#. module: generic_mixin
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_location_type_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_mixin_uniq_name_code_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_resource_type_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_group_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_level_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_name_uniq
msgid "Name must be unique."
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_name_by_sequence.py:0
#, python-format
msgid "New"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_no_update
msgid "Non Updatable"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__obsolete
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__obsolete
msgid "Obsolete"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__phone
msgid "Phone"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_contact.py:0
#, python-format
msgid "Telegram link must start with 'https://t.me/'"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__website_link
msgid "Website Link"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_xmlid
msgid "XML ID"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,help:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_xmlid
msgid "XML ID for this record."
msgstr ""
