# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_condition_automation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 15:06+0000\n"
"PO-Revision-Date: 2023-07-26 09:29+0000\n"
"Last-Translator: Den_Sharaf <<EMAIL>>\n"
"Language-Team: Ukrainian <http://weblate.crnd.pro/projects/"
"generic-addons-15-0/generic_condition_automation/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: generic_condition_automation
#: model:ir.model,name:generic_condition_automation.model_base_automation
msgid "Automated Action"
msgstr "Автоматизована дія"

#. module: generic_condition_automation
#: model:base.automation,name:generic_condition_automation.test_rule_on_write
#: model:ir.actions.server,name:generic_condition_automation.test_rule_on_write_ir_actions_server
msgid "Generic Condition: Test rule on write"
msgstr ""

#. module: generic_condition_automation
#: model:generic.condition,name:generic_condition_automation.demo_condition_partner_city_not_kyiv
msgid "Not (city: Kyiv)"
msgstr "Не (місто: Kyiv)"

#. module: generic_condition_automation
#: model:ir.model.fields,field_description:generic_condition_automation.field_base_automation__post_condition_ids
msgid "Post Conditions"
msgstr "Умови публікації"

#. module: generic_condition_automation
#: model:ir.model.fields,help:generic_condition_automation.field_base_automation__post_condition_ids
msgid "Post conditions (Generic conditions)"
msgstr "Умови публікації (Загальні умови)"

#. module: generic_condition_automation
#: model:ir.model.fields,field_description:generic_condition_automation.field_base_automation__pre_condition_ids
msgid "Pre Conditions"
msgstr "Попередні умови"

#. module: generic_condition_automation
#: model:ir.model.fields,help:generic_condition_automation.field_base_automation__pre_condition_ids
msgid "Pre conditions (Generic conditions)"
msgstr "Попередні умови (Загальні умови)"

#. module: generic_condition_automation
#: model_terms:ir.ui.view,arch_db:generic_condition_automation.view_base_automation_form
msgid "Set filter conditions:"
msgstr "Встановити умови фільтра:"

#. module: generic_condition_automation
#: model_terms:ir.ui.view,arch_db:generic_condition_automation.view_base_automation_form
msgid "Set search conditions:"
msgstr "Встановити умови пошуку:"

#~ msgid "Not (city: Kyiv)"
#~ msgstr "Не (місто: Kyiv)"
