<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tree_colored_field_brown_bg_dark_label"
            model="tree.colored.field">
        <field name="name">Simple text for demonstration light brown colored field dark label</field>
        <field name="bg_color">l_brown_bg</field>
        <field name="label_color">dark</field>
        <field name="field_colorized_by_state_expression">Test text background Light Gray label Dark Blue</field>
        <field name="label_state">ok</field>
        <field name="bg_state">ok</field>
    </record>

    <record id="tree_colored_field_green_bg_dark_label"
            model="tree.colored.field">
        <field name="name">Simple text for demonstration light green colored field dark label</field>
        <field name="bg_color">l_green_bg</field>
        <field name="label_color">dark</field>
        <field name="field_colorized_by_state_expression">Test text background Mint Green label Dark Blue</field>
        <field name="label_state">ok</field>
        <field name="bg_state">warning</field>
    </record>

    <record id="tree_colored_field_blue_bg_dark_label"
            model="tree.colored.field">
        <field name="name">Simple text for demonstration light blue colored field dark label</field>
        <field name="bg_color">l_blue_bg</field>
        <field name="label_color">dark</field>
        <field name="field_colorized_by_state_expression">Test text background Lavender label Dark Blue</field>
        <field name="label_state">ok</field>
        <field name="bg_state">fail</field>
    </record>

    <record id="tree_colored_field_brown_bg_white_label"
            model="tree.colored.field">
        <field name="name">Simple text for demonstration light brown colored field white label</field>
        <field name="bg_color">l_brown_bg</field>
        <field name="label_color">white</field>
        <field name="field_colorized_by_state_expression">Test text background Light Gray label Indigo</field>
        <field name="label_state">fail</field>
        <field name="bg_state">ok</field>
    </record>

    <record id="tree_colored_field_green_bg_white_label"
            model="tree.colored.field">
        <field name="name">Simple text for demonstration light green colored field white label</field>
        <field name="bg_color">l_green_bg</field>
        <field name="label_color">white</field>
        <field name="field_colorized_by_state_expression">Test text background Light Gray label Dark Slate Gray</field>
        <field name="label_state">warning</field>
        <field name="bg_state">ok</field>
    </record>

    <record id="tree_colored_field_blue_bg_white_label"
            model="tree.colored.field">
        <field name="name">Simple text for demonstration light blue colored field white label</field>
        <field name="bg_color">l_blue_bg</field>
        <field name="label_color">white</field>
        <field name="field_colorized_by_state_expression">Test text background Light Gray label Dark Blue</field>
        <field name="label_state">ok</field>
        <field name="bg_state">ok</field>
    </record>

</odoo>
