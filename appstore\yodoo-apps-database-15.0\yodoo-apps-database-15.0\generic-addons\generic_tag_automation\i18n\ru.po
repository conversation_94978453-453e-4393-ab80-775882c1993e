# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_tag_automation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 15:09+0000\n"
"PO-Revision-Date: 2019-09-13 15:09+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_tag_automation
#: model:ir.model.fields,field_description:generic_tag_automation.field_base_automation__state
#: model:ir.model.fields,field_description:generic_tag_automation.field_ir_actions_server__state
#: model:ir.model.fields,field_description:generic_tag_automation.field_ir_cron__state
msgid "Action To Do"
msgstr "Действие для выполнения"

#. module: generic_tag_automation
#: model:ir.model.fields,field_description:generic_tag_automation.field_base_automation__act_add_tag_ids
msgid "Add Tags"
msgstr ""

#. module: generic_tag_automation
#: model:ir.model,name:generic_tag_automation.model_base_automation
msgid "Automated Action"
msgstr "Автоматизированное действие"

#. module: generic_tag_automation
#: model:ir.model.fields,field_description:generic_tag_automation.field_base_automation__act_remove_tag_ids
msgid "Remove Tags"
msgstr ""

#. module: generic_tag_automation
#: model:ir.model,name:generic_tag_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Действие сервера"

#. module: generic_tag_automation
#: model:ir.model.fields.selection,name:generic_tag_automation.selection__ir_actions_server__state__set_tag
msgid "Set Tag"
msgstr ""

#. module: generic_tag_automation
#: model:ir.model.fields,help:generic_tag_automation.field_base_automation__act_add_tag_ids
msgid "Specify tags to be added to object this rule is applied to"
msgstr ""

#. module: generic_tag_automation
#: model:ir.model.fields,help:generic_tag_automation.field_base_automation__act_remove_tag_ids
msgid "Specify tags to be removed from object this rule is applied to"
msgstr ""

#. module: generic_tag_automation
#: model:ir.model.fields,help:generic_tag_automation.field_base_automation__state
#: model:ir.model.fields,help:generic_tag_automation.field_ir_actions_server__state
#: model:ir.model.fields,help:generic_tag_automation.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other "
"server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""

#~ msgid "Add Followers"
#~ msgstr "Добавить подписчиков"

#~ msgid "Create a new Record"
#~ msgstr "Создать новую Запись"

#~ msgid "Execute Python Code"
#~ msgstr "Выполнить код на Python"

#~ msgid "Execute several actions"
#~ msgstr "Выполните несколько действий"

#~ msgid "Send Email"
#~ msgstr "Отправить эл. письмо"
