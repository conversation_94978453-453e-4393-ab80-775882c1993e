<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record model="ir.ui.view" id="generic_location_form_view">
        <field name="model">generic.location</field>
        <field name="arch" type="xml">
            <form string="Generic Location">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_button_show_sublocations" 
                                class="oe_stat_button" 
                                type="object"
                                groups="generic_location.group_use_hierarchical_locations"
                                icon="fa-tasks">
                            <field string="Sublocation" 
                                    name="child_count" 
                                    widget="statinfo"/>
                        </button>
                        <button name="toggle_active" 
                                type="object"
                                class="oe_stat_button" 
                                icon="fa-archive">
                            <field name="active" widget="boolean_button"
                                   options='{"terminology": "archive"}'/>
                        </button>
                    </div>
                    <field name="image_1920" widget='image' class="oe_avatar" options='{"preview_image": "image_128"}'/>
                    <div class="oe_title" name="location_name">
                        <h1>
                            <field name="name"/>                          
                        </h1>
                        <h2 class="header_tags">
                            <field name="tag_ids"
                                   widget="many2many_tags"
                                   placeholder="Tags..."
                                   class="oe_inline"
                                   domain="[('model_id.model', '=', 'generic.location')]"
                                   context="{'default_model': 'generic.location'}"/>
                        </h2>
                    </div>
                    <group name="group_root">
                        <group name="group_root_left">
                            <field name="type_id" placeholder="Location Type"/>
                            <field name="partner_id" placeholder="Partner or customer..."/>
                        </group>
                        <group name="group_root_right">
                            <field name="parent_id"
                                   placeholder="Parent Location"
                                   groups="generic_location.group_use_hierarchical_locations"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Info" name="page_info">
                            <group name="group_info_root">
                                <group name="group_address_address" string="Address">
                                    <div class="o_address_format">
                                        <field name="street"
                                               class="o_address_street"
                                               placeholder="Street..."
                                               attrs="{'readonly': [('street_use_parent', '=', True)]}"/>
                                        <field name="street2"
                                               class="o_address_street"
                                               placeholder="Street 2..."
                                               attrs="{'readonly': [('street2_use_parent', '=', True)]}"/>
                                        <field name="city"
                                               class="o_address_city"
                                               placeholder="City"
                                               attrs="{'readonly': [('city_use_parent', '=', True)]}"/>
                                        <field name="state_id"
                                               class="o_address_state"
                                               placeholder="State"
                                               domain="[('country_id', '=', country_id)]"
                                               attrs="{'readonly': [('state_id_use_parent', '=', True)]}"
                                               options='{"no_open": True}'/>
                                        <field name="zip"
                                               class="o_address_zip"
                                               placeholder="ZIP"
                                               attrs="{'readonly': [('zip_use_parent', '=', True)]}"/>
                                        <field name="country_id"
                                               class="o_address_country"
                                               placeholder="Country"
                                               attrs="{'readonly': [('country_id_use_parent', '=', True)]}"
                                               options='{"no_open": True, "no_create": True}'/>
                                    </div>
                                </group>
                                <group name="group_address_config" string="Address Configuration"
                                       groups="generic_location.group_use_hierarchical_locations">
                                    <field name="street_use_parent"/>
                                    <field name="street2_use_parent"/>
                                    <field name="city_use_parent"/>
                                    <field name="state_id_use_parent"/>
                                    <field name="zip_use_parent"/>
                                    <field name="country_id_use_parent"/>
                                </group>
                                <group string="Geolocation" name="group_geolocation">
                                    <div name="geo_coordinates">
                                        <span class="oe_inline"> ( </span>
                                        <span> Lat : </span>
                                        <field name="latitude" class="oe_inline" no_label="1"/>
                                        <span> : Long : </span>
                                        <field name="longitude" class="oe_inline" nolabel="1"/>
                                        <span>) </span>
                                    </div>
                                </group>
                            </group>
                            <field name="description" placeholder="Description..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    
    <record model="ir.ui.view" id="generic_location_tree_view">
        <field name="model">generic.location</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="id" groups="base.group_no_one"/>
                <field name="type_id"/>
                <field name="partner_id"/>
                <field name="parent_id"
                       groups="generic_location.group_use_hierarchical_locations"/>
                <field name="street"/>
                <field name="street2"/>
                <field name="city"/>
                <field name="state_id"/>
                <field name="zip"/>
                <field name="country_id"/>
                <field name="active"/>
                <field name="tag_ids"
                       widget="many2many_tags"
                       placeholder="Tags..."
                       context="{'default_model': 'generic.location'}"/>
            </tree>
        </field>
    </record>
    
    <record model="ir.ui.view" id="generic_location_search_view">
        <field name="model">generic.location</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="partner_id"/>
                <field name="type_id"/>
                <field name="parent_id"
                       groups="generic_location.group_use_hierarchical_locations"/>
                <field name="tag_ids"/>
                <field name="search_tag_id"/>
                <field name="search_no_tag_id"/>
                <field name="state_id"/>
                <field name="zip"/>
                <field name="country_id"/>
                <separator/>

                <filter name="filter_active"
                        string="Active"
                        domain="[('active', '=', True)]"/>
                <filter name="filter_archived"
                        string="Archived"
                        domain="[('active', '=', False)]"/>
                <separator/>

                <filter name="filter_partner_locations"
                        string="Partner Locations"
                        domain="[('partner_id', '!=', False)]"/>
                <separator/>

                <group name="group_group_by" expand="0" string="Group by...">
                    <filter name="filter_group_by_type"
                            string="Type"
                            context="{'group_by': 'type_id'}"/>
                    <filter name="filter_group_by_partner"
                            string="Partner"
                            context="{'group_by': 'partner_id'}"/>
                    <filter name="filter_group_by_country"
                            string="Country"
                            context="{'group_by': 'country_id'}"/>
                    <filter name="filter_group_by_state"
                            string="State"
                            context="{'group_by': 'state_id'}"/>
                    <filter name="filter_group_by_city"
                            string="State"
                            context="{'group_by': 'city'}"/>
                </group>
            </search>
        </field>
     </record>

    <record id="generic_location_action" model="ir.actions.act_window">
        <field name="name">Location</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">generic.location</field>
    </record>

    <record id="generic_location_action_view_tree"
            model="ir.actions.act_window.view">
        <field eval="5" name="sequence"/>
        <field name="view_mode">tree</field>
        <field name="view_id"  ref="generic_location_tree_view"/>
        <field name="act_window_id" ref="generic_location_action"/>
    </record>

    <record id="generic_location_action_view_from"
            model="ir.actions.act_window.view">
        <field eval="10" name="sequence"/>
        <field name="view_mode">form</field>
        <field name="view_id"  ref="generic_location_form_view"/>
        <field name="act_window_id" ref="generic_location_action"/>
    </record>


    <menuitem id="menu_generic_location_root"
              name="Locations"
              groups="generic_location.group_generic_location_user"
              web_icon="generic_location,static/description/icon.png"
              sequence="13"/>

    <menuitem id="menu_generic_location"
              name="Location"
              parent="menu_generic_location_root"
              sequence="10"
              action="generic_location_action"/>

    <menuitem id="menu_configuration"
              name="Configuration"
              parent="menu_generic_location_root"
              groups="generic_location.group_generic_location_manager"
              sequence="60"/>

</odoo>
