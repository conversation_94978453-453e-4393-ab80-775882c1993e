<!DOCTYPE html>
<!-- Generated with Dr.js -->
<html lang="en"><head><meta charset="utf-8"><title><PERSON><PERSON><PERSON> Reference</title><link rel="stylesheet" href="dr.css" media="screen"><link rel="stylesheet" href="dr-print.css" media="print"></head><body id="dr-js"><div id="dr"><ol class="dr-toc" id="dr-toc"><li class="dr-lvl0"><a href="#Animation" class="{clas}"><span>Animation</span></a></li><li class="dr-lvl1"><a href="#Animation.delay" class="dr-method"><span>Animation.delay()</span></a></li><li class="dr-lvl1"><a href="#Animation.repeat" class="dr-method"><span>Animation.repeat()</span></a></li><li class="dr-lvl0"><a href="#Element" class="{clas}"><span>Element</span></a></li><li class="dr-lvl1"><a href="#Element.animate" class="dr-method"><span>Element.animate()</span></a></li><li class="dr-lvl1"><a href="#Element.animateWith" class="dr-method"><span>Element.animateWith()</span></a></li><li class="dr-lvl1"><a href="#Element.attr" class="dr-method"><span>Element.attr()</span></a></li><li class="dr-lvl1"><a href="#Element.click" class="dr-method"><span>Element.click()</span></a></li><li class="dr-lvl1"><a href="#Element.clone" class="dr-method"><span>Element.clone()</span></a></li><li class="dr-lvl1"><a href="#Element.data" class="dr-method"><span>Element.data()</span></a></li><li class="dr-lvl1"><a href="#Element.dblclick" class="dr-method"><span>Element.dblclick()</span></a></li><li class="dr-lvl1"><a href="#Element.drag" class="dr-method"><span>Element.drag()</span></a></li><li class="dr-lvl1"><a href="#Element.getBBox" class="dr-method"><span>Element.getBBox()</span></a></li><li class="dr-lvl1"><a href="#Element.getPointAtLength" class="dr-method"><span>Element.getPointAtLength()</span></a></li><li class="dr-lvl1"><a href="#Element.getSubpath" class="dr-method"><span>Element.getSubpath()</span></a></li><li class="dr-lvl1"><a href="#Element.getTotalLength" class="dr-method"><span>Element.getTotalLength()</span></a></li><li class="dr-lvl1"><a href="#Element.glow" class="dr-method"><span>Element.glow()</span></a></li><li class="dr-lvl1"><a href="#Element.hide" class="dr-method"><span>Element.hide()</span></a></li><li class="dr-lvl1"><a href="#Element.hover" class="dr-method"><span>Element.hover()</span></a></li><li class="dr-lvl1"><a href="#Element.id" class="dr-property"><span>Element.id</span></a></li><li class="dr-lvl1"><a href="#Element.insertAfter" class="dr-method"><span>Element.insertAfter()</span></a></li><li class="dr-lvl1"><a href="#Element.insertBefore" class="dr-method"><span>Element.insertBefore()</span></a></li><li class="dr-lvl1"><a href="#Element.mousedown" class="dr-method"><span>Element.mousedown()</span></a></li><li class="dr-lvl1"><a href="#Element.mousemove" class="dr-method"><span>Element.mousemove()</span></a></li><li class="dr-lvl1"><a href="#Element.mouseout" class="dr-method"><span>Element.mouseout()</span></a></li><li class="dr-lvl1"><a href="#Element.mouseover" class="dr-method"><span>Element.mouseover()</span></a></li><li class="dr-lvl1"><a href="#Element.mouseup" class="dr-method"><span>Element.mouseup()</span></a></li><li class="dr-lvl1"><a href="#Element.next" class="dr-property"><span>Element.next</span></a></li><li class="dr-lvl1"><a href="#Element.node" class="dr-property"><span>Element.node</span></a></li><li class="dr-lvl1"><a href="#Element.onDragOver" class="dr-method"><span>Element.onDragOver()</span></a></li><li class="dr-lvl1"><a href="#Element.paper" class="dr-property"><span>Element.paper</span></a></li><li class="dr-lvl1"><a href="#Element.pause" class="dr-method"><span>Element.pause()</span></a></li><li class="dr-lvl1"><a href="#Element.prev" class="dr-property"><span>Element.prev</span></a></li><li class="dr-lvl1"><a href="#Element.raphael" class="dr-property"><span>Element.raphael</span></a></li><li class="dr-lvl1"><a href="#Element.remove" class="dr-method"><span>Element.remove()</span></a></li><li class="dr-lvl1"><a href="#Element.removeData" class="dr-method"><span>Element.removeData()</span></a></li><li class="dr-lvl1"><a href="#Element.resume" class="dr-method"><span>Element.resume()</span></a></li><li class="dr-lvl1"><a href="#Element.rotate" class="dr-method"><span>Element.rotate()</span></a></li><li class="dr-lvl1"><a href="#Element.scale" class="dr-method"><span>Element.scale()</span></a></li><li class="dr-lvl1"><a href="#Element.setTime" class="dr-method"><span>Element.setTime()</span></a></li><li class="dr-lvl1"><a href="#Element.show" class="dr-method"><span>Element.show()</span></a></li><li class="dr-lvl1"><a href="#Element.status" class="dr-method"><span>Element.status()</span></a></li><li class="dr-lvl1"><a href="#Element.stop" class="dr-method"><span>Element.stop()</span></a></li><li class="dr-lvl1"><a href="#Element.toBack" class="dr-method"><span>Element.toBack()</span></a></li><li class="dr-lvl1"><a href="#Element.toFront" class="dr-method"><span>Element.toFront()</span></a></li><li class="dr-lvl1"><a href="#Element.touchcancel" class="dr-method"><span>Element.touchcancel()</span></a></li><li class="dr-lvl1"><a href="#Element.touchend" class="dr-method"><span>Element.touchend()</span></a></li><li class="dr-lvl1"><a href="#Element.touchmove" class="dr-method"><span>Element.touchmove()</span></a></li><li class="dr-lvl1"><a href="#Element.touchstart" class="dr-method"><span>Element.touchstart()</span></a></li><li class="dr-lvl1"><a href="#Element.transform" class="dr-method"><span>Element.transform()</span></a></li><li class="dr-lvl1"><a href="#Element.translate" class="dr-method"><span>Element.translate()</span></a></li><li class="dr-lvl1"><a href="#Element.unclick" class="dr-method"><span>Element.unclick()</span></a></li><li class="dr-lvl1"><a href="#Element.undblclick" class="dr-method"><span>Element.undblclick()</span></a></li><li class="dr-lvl1"><a href="#Element.undrag" class="dr-method"><span>Element.undrag()</span></a></li><li class="dr-lvl1"><a href="#Element.unhover" class="dr-method"><span>Element.unhover()</span></a></li><li class="dr-lvl1"><a href="#Element.unmousedown" class="dr-method"><span>Element.unmousedown()</span></a></li><li class="dr-lvl1"><a href="#Element.unmousemove" class="dr-method"><span>Element.unmousemove()</span></a></li><li class="dr-lvl1"><a href="#Element.unmouseout" class="dr-method"><span>Element.unmouseout()</span></a></li><li class="dr-lvl1"><a href="#Element.unmouseover" class="dr-method"><span>Element.unmouseover()</span></a></li><li class="dr-lvl1"><a href="#Element.unmouseup" class="dr-method"><span>Element.unmouseup()</span></a></li><li class="dr-lvl1"><a href="#Element.untouchcancel" class="dr-method"><span>Element.untouchcancel()</span></a></li><li class="dr-lvl1"><a href="#Element.untouchend" class="dr-method"><span>Element.untouchend()</span></a></li><li class="dr-lvl1"><a href="#Element.untouchmove" class="dr-method"><span>Element.untouchmove()</span></a></li><li class="dr-lvl1"><a href="#Element.untouchstart" class="dr-method"><span>Element.untouchstart()</span></a></li><li class="dr-lvl0"><a href="#Matrix" class="{clas}"><span>Matrix</span></a></li><li class="dr-lvl1"><a href="#Matrix.add" class="dr-method"><span>Matrix.add()</span></a></li><li class="dr-lvl1"><a href="#Matrix.clone" class="dr-method"><span>Matrix.clone()</span></a></li><li class="dr-lvl1"><a href="#Matrix.invert" class="dr-method"><span>Matrix.invert()</span></a></li><li class="dr-lvl1"><a href="#Matrix.rotate" class="dr-method"><span>Matrix.rotate()</span></a></li><li class="dr-lvl1"><a href="#Matrix.scale" class="dr-method"><span>Matrix.scale()</span></a></li><li class="dr-lvl1"><a href="#Matrix.split" class="dr-method"><span>Matrix.split()</span></a></li><li class="dr-lvl1"><a href="#Matrix.toTransformString" class="dr-method"><span>Matrix.toTransformString()</span></a></li><li class="dr-lvl1"><a href="#Matrix.translate" class="dr-method"><span>Matrix.translate()</span></a></li><li class="dr-lvl1"><a href="#Matrix.x" class="dr-method"><span>Matrix.x()</span></a></li><li class="dr-lvl1"><a href="#Matrix.y" class="dr-method"><span>Matrix.y()</span></a></li><li class="dr-lvl0"><a href="#Paper" class="{clas}"><span>Paper</span></a></li><li class="dr-lvl1"><a href="#Paper.add" class="dr-method"><span>Paper.add()</span></a></li><li class="dr-lvl1"><a href="#Paper.bottom" class="dr-property"><span>Paper.bottom</span></a></li><li class="dr-lvl1"><a href="#Paper.ca" class="dr-property"><span>Paper.ca</span></a></li><li class="dr-lvl1"><a href="#Paper.circle" class="dr-method"><span>Paper.circle()</span></a></li><li class="dr-lvl1"><a href="#Paper.clear" class="dr-method"><span>Paper.clear()</span></a></li><li class="dr-lvl1"><a href="#Paper.customAttributes" class="dr-property"><span>Paper.customAttributes</span></a></li><li class="dr-lvl1"><a href="#Paper.ellipse" class="dr-method"><span>Paper.ellipse()</span></a></li><li class="dr-lvl1"><a href="#Paper.forEach" class="dr-method"><span>Paper.forEach()</span></a></li><li class="dr-lvl1"><a href="#Paper.getById" class="dr-method"><span>Paper.getById()</span></a></li><li class="dr-lvl1"><a href="#Paper.getElementByPoint" class="dr-method"><span>Paper.getElementByPoint()</span></a></li><li class="dr-lvl1"><a href="#Paper.getFont" class="dr-method"><span>Paper.getFont()</span></a></li><li class="dr-lvl1"><a href="#Paper.image" class="dr-method"><span>Paper.image()</span></a></li><li class="dr-lvl1"><a href="#Paper.path" class="dr-method"><span>Paper.path()</span></a></li><li class="dr-lvl1"><a href="#Paper.print" class="dr-method"><span>Paper.print()</span></a></li><li class="dr-lvl1"><a href="#Paper.raphael" class="dr-property"><span>Paper.raphael</span></a></li><li class="dr-lvl1"><a href="#Paper.rect" class="dr-method"><span>Paper.rect()</span></a></li><li class="dr-lvl1"><a href="#Paper.remove" class="dr-method"><span>Paper.remove()</span></a></li><li class="dr-lvl1"><a href="#Paper.renderfix" class="dr-method"><span>Paper.renderfix()</span></a></li><li class="dr-lvl1"><a href="#Paper.safari" class="dr-method"><span>Paper.safari()</span></a></li><li class="dr-lvl1"><a href="#Paper.set" class="dr-method"><span>Paper.set()</span></a></li><li class="dr-lvl1"><a href="#Paper.setFinish" class="dr-method"><span>Paper.setFinish()</span></a></li><li class="dr-lvl1"><a href="#Paper.setSize" class="dr-method"><span>Paper.setSize()</span></a></li><li class="dr-lvl1"><a href="#Paper.setStart" class="dr-method"><span>Paper.setStart()</span></a></li><li class="dr-lvl1"><a href="#Paper.setViewBox" class="dr-method"><span>Paper.setViewBox()</span></a></li><li class="dr-lvl1"><a href="#Paper.text" class="dr-method"><span>Paper.text()</span></a></li><li class="dr-lvl1"><a href="#Paper.top" class="dr-property"><span>Paper.top</span></a></li><li class="dr-lvl0"><a href="#Raphael" class="dr-method"><span>Raphael()</span></a></li><li class="dr-lvl1"><a href="#Raphael.angle" class="dr-method"><span>Raphael.angle()</span></a></li><li class="dr-lvl1"><a href="#Raphael.animation" class="dr-method"><span>Raphael.animation()</span></a></li><li class="dr-lvl1"><a href="#Raphael.color" class="dr-method"><span>Raphael.color()</span></a></li><li class="dr-lvl1"><a href="#Raphael.createUUID" class="dr-method"><span>Raphael.createUUID()</span></a></li><li class="dr-lvl1"><a href="#Raphael.deg" class="dr-method"><span>Raphael.deg()</span></a></li><li class="dr-lvl1"><a href="#Raphael.easing_formulas" class="dr-property"><span>Raphael.easing_formulas</span></a></li><li class="dr-lvl1"><a href="#Raphael.el" class="dr-property"><span>Raphael.el</span></a></li><li class="dr-lvl1"><a href="#Raphael.findDotsAtSegment" class="dr-method"><span>Raphael.findDotsAtSegment()</span></a></li><li class="dr-lvl1"><a href="#Raphael.fn" class="dr-property"><span>Raphael.fn</span></a></li><li class="dr-lvl1"><a href="#Raphael.format" class="dr-method"><span>Raphael.format()</span></a></li><li class="dr-lvl1"><a href="#Raphael.fullfill" class="dr-method"><span>Raphael.fullfill()</span></a></li><li class="dr-lvl1"><a href="#Raphael.getColor" class="dr-method"><span>Raphael.getColor()</span></a></li><li class="dr-lvl2"><a href="#Raphael.getColor.reset" class="dr-method"><span>Raphael.getColor.reset()</span></a></li><li class="dr-lvl1"><a href="#Raphael.getPointAtLength" class="dr-method"><span>Raphael.getPointAtLength()</span></a></li><li class="dr-lvl1"><a href="#Raphael.getRGB" class="dr-method"><span>Raphael.getRGB()</span></a></li><li class="dr-lvl1"><a href="#Raphael.getSubpath" class="dr-method"><span>Raphael.getSubpath()</span></a></li><li class="dr-lvl1"><a href="#Raphael.getTotalLength" class="dr-method"><span>Raphael.getTotalLength()</span></a></li><li class="dr-lvl1"><a href="#Raphael.hsb" class="dr-method"><span>Raphael.hsb()</span></a></li><li class="dr-lvl1"><a href="#Raphael.hsb2rgb" class="dr-method"><span>Raphael.hsb2rgb()</span></a></li><li class="dr-lvl1"><a href="#Raphael.hsl" class="dr-method"><span>Raphael.hsl()</span></a></li><li class="dr-lvl1"><a href="#Raphael.hsl2rgb" class="dr-method"><span>Raphael.hsl2rgb()</span></a></li><li class="dr-lvl1"><a href="#Raphael.is" class="dr-method"><span>Raphael.is()</span></a></li><li class="dr-lvl1"><a href="#Raphael.matrix" class="dr-method"><span>Raphael.matrix()</span></a></li><li class="dr-lvl1"><a href="#Raphael.ninja" class="dr-method"><span>Raphael.ninja()</span></a></li><li class="dr-lvl1"><a href="#Raphael.parsePathString" class="dr-method"><span>Raphael.parsePathString()</span></a></li><li class="dr-lvl1"><a href="#Raphael.parseTransformString" class="dr-method"><span>Raphael.parseTransformString()</span></a></li><li class="dr-lvl1"><a href="#Raphael.path2curve" class="dr-method"><span>Raphael.path2curve()</span></a></li><li class="dr-lvl1"><a href="#Raphael.pathToRelative" class="dr-method"><span>Raphael.pathToRelative()</span></a></li><li class="dr-lvl1"><a href="#Raphael.rad" class="dr-method"><span>Raphael.rad()</span></a></li><li class="dr-lvl1"><a href="#Raphael.registerFont" class="dr-method"><span>Raphael.registerFont()</span></a></li><li class="dr-lvl1"><a href="#Raphael.rgb" class="dr-method"><span>Raphael.rgb()</span></a></li><li class="dr-lvl1"><a href="#Raphael.rgb2hsb" class="dr-method"><span>Raphael.rgb2hsb()</span></a></li><li class="dr-lvl1"><a href="#Raphael.rgb2hsl" class="dr-method"><span>Raphael.rgb2hsl()</span></a></li><li class="dr-lvl1"><a href="#Raphael.setWindow" class="dr-method"><span>Raphael.setWindow()</span></a></li><li class="dr-lvl1"><a href="#Raphael.snapTo" class="dr-method"><span>Raphael.snapTo()</span></a></li><li class="dr-lvl1"><a href="#Raphael.st" class="dr-property"><span>Raphael.st</span></a></li><li class="dr-lvl1"><a href="#Raphael.svg" class="dr-property"><span>Raphael.svg</span></a></li><li class="dr-lvl1"><a href="#Raphael.type" class="dr-property"><span>Raphael.type</span></a></li><li class="dr-lvl1"><a href="#Raphael.vml" class="dr-property"><span>Raphael.vml</span></a></li><li class="dr-lvl0"><a href="#Set" class="{clas}"><span>Set</span></a></li><li class="dr-lvl1"><a href="#Set.clear" class="dr-method"><span>Set.clear()</span></a></li><li class="dr-lvl1"><a href="#Set.exclude" class="dr-method"><span>Set.exclude()</span></a></li><li class="dr-lvl1"><a href="#Set.forEach" class="dr-method"><span>Set.forEach()</span></a></li><li class="dr-lvl1"><a href="#Set.pop" class="dr-method"><span>Set.pop()</span></a></li><li class="dr-lvl1"><a href="#Set.push" class="dr-method"><span>Set.push()</span></a></li><li class="dr-lvl1"><a href="#Set.splice" class="dr-method"><span>Set.splice()</span></a></li><li class="dr-lvl0"><a href="#eve" class="dr-method"><span>eve()</span></a></li><li class="dr-lvl1"><a href="#eve.listeners" class="dr-method"><span>eve.listeners()</span></a></li><li class="dr-lvl1"><a href="#eve.nt" class="dr-method"><span>eve.nt()</span></a></li><li class="dr-lvl1"><a href="#eve.off" class="dr-method"><span>eve.off()</span></a></li><li class="dr-lvl1"><a href="#eve.on" class="dr-method"><span>eve.on()</span></a></li><li class="dr-lvl1"><a href="#eve.once" class="dr-method"><span>eve.once()</span></a></li><li class="dr-lvl1"><a href="#eve.stop" class="dr-method"><span>eve.stop()</span></a></li><li class="dr-lvl1"><a href="#eve.unbind" class="dr-method"><span>eve.unbind()</span></a></li><li class="dr-lvl1"><a href="#eve.version" class="dr-property"><span>eve.version</span></a></li></ol><div class="dr-doc"><h1>Raphaël Reference</h1><div class="Animation-section"><h2 id="Animation" class="undefined"><i class="dr-trixie">&#160;</i>Animation<a href="#Animation" title="Link to this section" class="dr-hash">&#x2693;</a></h2>
<div class="extra" id="Animation-extra"></div></div><div class="Animation-delay-section"><h3 id="Animation.delay" class="dr-method"><i class="dr-trixie">&#160;</i>Animation.delay(delay)<a href="#Animation.delay" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3738 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3738">&#x27ad;</a></h3>
<div class="extra" id="Animation.delay-extra"></div></div><div class="dr-method"><p>Creates a copy of existing animation object with given delay.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">delay</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number of ms to pass between animation start and actual animation</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">new altered Animation object</span></p>
<pre class="javascript code"><code><b>var</b> anim<span class="s"> = </span>Raphael.animation({cx: <span class="d">10</span>, cy: <span class="d">20</span>}, <span class="d">2e3</span>);
circle1.animate(anim); <span class="c">// run the given animation immediately</span>
circle2.animate(anim.delay(<span class="d">500</span>)); <span class="c">// run the given animation after <span class="d">500</span> ms</span>
</code></pre>
</div><div class="Animation-repeat-section"><h3 id="Animation.repeat" class="dr-method"><i class="dr-trixie">&#160;</i>Animation.repeat(repeat)<a href="#Animation.repeat" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3756 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3756">&#x27ad;</a></h3>
<div class="extra" id="Animation.repeat-extra"></div></div><div class="dr-method"><p>Creates a copy of existing animation object with given repetition.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">repeat</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number iterations of animation. For infinite animation pass <code>Infinity</code></dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">new altered Animation object</span></p>
</div><div class="Element-section"><h2 id="Element" class="undefined"><i class="dr-trixie">&#160;</i>Element<a href="#Element" title="Link to this section" class="dr-hash">&#x2693;</a></h2>
<div class="extra" id="Element-extra"></div></div><div class="Element-animate-section"><h3 id="Element.animate" class="dr-method"><i class="dr-trixie">&#160;</i>Element.animate(…)<a href="#Element.animate" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4026 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4026">&#x27ad;</a></h3>
<div class="extra" id="Element.animate-extra"></div></div><div class="dr-method"><p>Creates and starts animation for given element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">params</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">final attributes for the element, see also <a href="#Element.attr" class="dr-link">Element.attr</a></dd>
<dt class="dr-param">ms</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number of milliseconds for animation to run</dd>
<dt class="dr-param optional">easing</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">easing type. Accept one of <a href="#Raphael.easing_formulas" class="dr-link">Raphael.easing_formulas</a> or CSS format: <code>cubic&#x2010;bezier(XX,&#160;XX,&#160;XX,&#160;XX)</code></dd>
<dt class="dr-param optional">callback</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">callback function. Will be called at the end of animation.</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">animation</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation object, see <a href="#Raphael.animation" class="dr-link">Raphael.animation</a></dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element</span></p>
</div><div class="Element-animateWith-section"><h3 id="Element.animateWith" class="dr-method"><i class="dr-trixie">&#160;</i>Element.animateWith(…)<a href="#Element.animateWith" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3629 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3629">&#x27ad;</a></h3>
<div class="extra" id="Element.animateWith-extra"></div></div><div class="dr-method"><p>Acts similar to <a href="#Element.animate" class="dr-link">Element.animate</a>, but ensure that given animation runs in sync with another given element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">el</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">element to sync with</dd>
<dt class="dr-param">anim</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation to sync with</dd>
<dt class="dr-param optional">params</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">final attributes for the element, see also <a href="#Element.attr" class="dr-link">Element.attr</a></dd>
<dt class="dr-param optional">ms</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number of milliseconds for animation to run</dd>
<dt class="dr-param optional">easing</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">easing type. Accept on of <a href="#Raphael.easing_formulas" class="dr-link">Raphael.easing_formulas</a> or CSS format: <code>cubic&#x2010;bezier(XX,&#160;XX,&#160;XX,&#160;XX)</code></dd>
<dt class="dr-param optional">callback</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">callback function. Will be called at the end of animation.</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">element</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">element to sync with</dd>
<dt class="dr-param">anim</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation to sync with</dd>
<dt class="dr-param optional">animation</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation object, see <a href="#Raphael.animation" class="dr-link">Raphael.animation</a></dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element</span></p>
</div><div class="Element-attr-section"><h3 id="Element.attr" class="dr-method"><i class="dr-trixie">&#160;</i>Element.attr(…)<a href="#Element.attr" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 985 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L985">&#x27ad;</a></h3>
<div class="extra" id="Element.attr-extra"></div></div><div class="dr-method"><p>Sets the attributes of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">attrName</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">attribute’s name</dd>
<dt class="dr-param">value</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">value</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">params</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">object of name/value pairs</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">attrName</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">attribute’s name</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">attrNames</dt>
<dd class="dr-type"><em class="dr-type-array">array</em></dd>
<dd class="dr-description">in this case method returns array of current values for given attribute names</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a> if attrsName <em class="amp">&amp;</em> value or params are passed in.</span></p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-...">...</em> <span class="dr-description">value of the attribute if only attrsName is passed in.</span></p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-array">array</em> <span class="dr-description">array of values of the attribute if attrsNames is passed in.</span></p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">object of attributes if nothing is passed in.</span></p>
<p class="header">Possible parameters
</p>
<p>Please refer to the <a href="http://www.w3.org/TR/SVG/" title="The W3C Recommendation for the SVG language describes these properties in detail.">SVG specification</a> for an explanation of these parameters.</p>
<ol class="dr-json"><li><span class="dr-json-key">arrow-end</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">arrowhead on the end of the path. The format for string is <code>&lt;type>[-&lt;width>[-&lt;length>]]</code>. Possible types: <code>classic</code>, <code>block</code>, <code>open</code>, <code>oval</code>, <code>diamond</code>, <code>none</code>, width: <code>wide</code>, <code>narrow</code>, <code>midium</code>, length: <code>long</code>, <code>short</code>, <code>midium</code>.</span>
<li><span class="dr-json-key">clip-rect</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">comma or space separated values: x, y, width and height</span>
<li><span class="dr-json-key">cursor</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">CSS type of the cursor</span>
<li><span class="dr-json-key">cx</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">the x-axis coordinate of the center of the circle, or ellipse</span>
<li><span class="dr-json-key">cy</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">the y-axis coordinate of the center of the circle, or ellipse</span>
<li><span class="dr-json-key">fill</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">colour, gradient or image</span>
<li><span class="dr-json-key">fill-opacity</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">font</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">font-family</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">font-size</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">font size in pixels</span>
<li><span class="dr-json-key">font-weight</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">height</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">href</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">URL, if specified element behaves as hyperlink</span>
<li><span class="dr-json-key">opacity</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">path</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">SVG path string format</span>
<li><span class="dr-json-key">r</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">radius of the circle, ellipse or rounded corner on the rect</span>
<li><span class="dr-json-key">rx</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">horisontal radius of the ellipse</span>
<li><span class="dr-json-key">ry</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">vertical radius of the ellipse</span>
<li><span class="dr-json-key">src</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">image URL, only works for <a href="#Element.image" class="dr-link">Element.image</a> element</span>
<li><span class="dr-json-key">stroke</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">stroke colour</span>
<li><span class="dr-json-key">stroke-dasharray</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">[“”, “<code>-</code>”, “<code>.</code>”, “<code>-.</code>”, “<code>-..</code>”, “<code>. </code>”, “<code>- </code>”, “<code>--</code>”, “<code>- .</code>”, “<code>--.</code>”, “<code>--..</code>”]</span>
<li><span class="dr-json-key">stroke-linecap</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">[“<code>butt</code>”, “<code>square</code>”, “<code>round</code>”]</span>
<li><span class="dr-json-key">stroke-linejoin</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">[“<code>bevel</code>”, “<code>round</code>”, “<code>miter</code>”]</span>
<li><span class="dr-json-key">stroke-miterlimit</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">stroke-opacity</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">stroke-width</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">stroke width in pixels, default is '1'</span>
<li><span class="dr-json-key">target</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">used with href</span>
<li><span class="dr-json-key">text</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">contents of the text element. Use <code>\n</code> for multiline text</span>
<li><span class="dr-json-key">text-anchor</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">[“<code>start</code>”, “<code>middle</code>”, “<code>end</code>”], default is “<code>middle</code>”</span>
<li><span class="dr-json-key">title</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">will create tooltip with a given text</span>
<li><span class="dr-json-key">transform</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">see <a href="#Element.transform" class="dr-link">Element.transform</a></span>
<li><span class="dr-json-key">width</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">x</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
<li><span class="dr-json-key">y</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">&#160;</span>
</ol>
<p class="header">Gradients
<p>Linear gradient format: “<code>‹angle›-‹colour›[-‹colour›[:‹offset›]]*-‹colour›</code>”, example: “<code>90-#fff-#000</code>” – 90°
gradient from white to black or “<code>0-#fff-#f00:20-#000</code>” – 0° gradient from white via red (at 20%) to black.
</p>
<p>radial gradient: “<code>r[(‹fx›, ‹fy›)]‹colour›[-‹colour›[:‹offset›]]*-‹colour›</code>”, example: “<code>r#fff-#000</code>” –
gradient from white to black or “<code>r(0.25, 0.75)#fff-#000</code>” – gradient from white to black with focus point
at 0.25, 0.75. Focus point coordinates are in 0..1 range. Radial gradients can only be applied to circles and ellipses.
</p>
<p class="header">Path String
</p>
<p>Please refer to <a href="http://www.w3.org/TR/SVG/paths.html#PathData" title="Details of a path’s data attribute’s format are described in the SVG specification.">SVG documentation regarding path string</a>. Raphaël fully supports it.</p>
<p class="header">Colour Parsing
</p>
<ul>
    <li>Colour name (“<code>red</code>”, “<code>green</code>”, “<code>cornflowerblue</code>”, etc)</li>
    <li>#••• — shortened HTML colour: (“<code>#000</code>”, “<code>#fc0</code>”, etc)</li>
    <li>#•••••• — full length HTML colour: (“<code>#000000</code>”, “<code>#bd2300</code>”)</li>
    <li>rgb(•••, •••, •••) — red, green and blue channels’ values: (“<code>rgb(200,&nbsp;100,&nbsp;0)</code>”)</li>
    <li>rgb(•••%, •••%, •••%) — same as above, but in %: (“<code>rgb(100%,&nbsp;175%,&nbsp;0%)</code>”)</li>
    <li>rgba(•••, •••, •••, •••) — red, green and blue channels’ values: (“<code>rgba(200,&nbsp;100,&nbsp;0, .5)</code>”)</li>
    <li>rgba(•••%, •••%, •••%, •••%) — same as above, but in %: (“<code>rgba(100%,&nbsp;175%,&nbsp;0%, 50%)</code>”)</li>
    <li>hsb(•••, •••, •••) — hue, saturation and brightness values: (“<code>hsb(0.5,&nbsp;0.25,&nbsp;1)</code>”)</li>
    <li>hsb(•••%, •••%, •••%) — same as above, but in %</li>
    <li>hsba(•••, •••, •••, •••) — same as above, but with opacity</li>
    <li>hsl(•••, •••, •••) — almost the same as hsb, see <a href="http://en.wikipedia.org/wiki/HSL_and_HSV" title="HSL and HSV - Wikipedia, the free encyclopedia">Wikipedia page</a></li>
    <li>hsl(•••%, •••%, •••%) — same as above, but in %</li>
    <li>hsla(•••, •••, •••, •••) — same as above, but with opacity</li>
    <li>Optionally for hsb and hsl you could specify hue as a degree: “<code>hsl(240deg,&nbsp;1,&nbsp;.5)</code>” or, if you want to go fancy, “<code>hsl(240°,&nbsp;1,&nbsp;.5)</code>”</li>
</ul>
</div><div class="Element-click-section"><h3 id="Element.click" class="dr-method"><i class="dr-trixie">&#160;</i>Element.click(handler)<a href="#Element.click" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2354 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2354">&#x27ad;</a></h3>
<div class="extra" id="Element.click-extra"></div></div><div class="dr-method"><p>Adds event handler for click for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-clone-section"><h3 id="Element.clone" class="dr-method"><i class="dr-trixie">&#160;</i>Element.clone()<a href="#Element.clone" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3150 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3150">&#x27ad;</a></h3>
<div class="extra" id="Element.clone-extra"></div></div><div class="dr-method"><p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">clone of a given element</span></p>
</div><div class="Element-data-section"><h3 id="Element.data" class="dr-method"><i class="dr-trixie">&#160;</i>Element.data(key, [value])<a href="#Element.data" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2599 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2599">&#x27ad;</a></h3>
<div class="extra" id="Element.data-extra"></div></div><div class="dr-method"><p>Adds or retrieves given value asociated with given key.
See also <a href="#Element.removeData" class="dr-link">Element.removeData</a>
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">key</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">key to store data</dd>
<dt class="dr-param optional">value</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-any">any</em></dd>
<dd class="dr-description">value to store</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
<p>or, if value is not specified:
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-any">any</em> <span class="dr-description">value</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>for</b> (<b>var</b> i<span class="s"> = </span><span class="d">0</span>, i &lt; <span class="d">5</span>, i++) {
    paper.circle(<span class="d">10</span><span class="s"> + </span><span class="d">15</span><span class="s"> * </span>i, <span class="d">10</span>, <span class="d">10</span>)
         .attr({fill: <i>"#<span class="d">000</span>"</i>})
         .data(<i>"i"</i>, i)
         .click(<b>function</b> () {
            alert(<b>this</b>.data(<i>"i"</i>));
         });
}
</code></pre>
</div><div class="Element-dblclick-section"><h3 id="Element.dblclick" class="dr-method"><i class="dr-trixie">&#160;</i>Element.dblclick(handler)<a href="#Element.dblclick" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2373 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2373">&#x27ad;</a></h3>
<div class="extra" id="Element.dblclick-extra"></div></div><div class="dr-method"><p>Adds event handler for double click for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-drag-section"><h3 id="Element.drag" class="dr-method"><i class="dr-trixie">&#160;</i>Element.drag(onmove, onstart, onend, [mcontext], [scontext], [econtext])<a href="#Element.drag" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2692 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2692">&#x27ad;</a></h3>
<div class="extra" id="Element.drag-extra"></div></div><div class="dr-method"><p>Adds event handlers for drag of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">onmove</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for moving</dd>
<dt class="dr-param">onstart</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for drag start</dd>
<dt class="dr-param">onend</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for drag end</dd>
<dt class="dr-param optional">mcontext</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context for moving handler</dd>
<dt class="dr-param optional">scontext</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context for drag start handler</dd>
<dt class="dr-param optional">econtext</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context for drag end handler</dd>
</dl>
<p>Additionaly following <code>drag</code> events will be triggered: <code>drag.start.&lt;id></code> on start, 
<code>drag.end.&lt;id></code> on end and <code>drag.move.&lt;id></code> on every move. When element will be dragged over another element 
<code>drag.over.&lt;id></code> will be fired as well.
</p>
<p>Start event and start handler will be called in specified context or in context of the element with following parameters:
</p>
<ol class="dr-json"><li><span class="dr-json-key">x</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x position of the mouse</span>
<li><span class="dr-json-key">y</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y position of the mouse</span>
<li><span class="dr-json-key">event</span><span class="dr-type"><em class="dr-type-object">object</em></span><span class="dr-json-description">DOM event object</span>
</ol>
<p>Move event and move handler will be called in specified context or in context of the element with following parameters:
</p>
<ol class="dr-json"><li><span class="dr-json-key">dx</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">shift by x from the start point</span>
<li><span class="dr-json-key">dy</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">shift by y from the start point</span>
<li><span class="dr-json-key">x</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x position of the mouse</span>
<li><span class="dr-json-key">y</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y position of the mouse</span>
<li><span class="dr-json-key">event</span><span class="dr-type"><em class="dr-type-object">object</em></span><span class="dr-json-description">DOM event object</span>
</ol>
<p>End event and end handler will be called in specified context or in context of the element with following parameters:
</p>
<ol class="dr-json"><li><span class="dr-json-key">event</span><span class="dr-type"><em class="dr-type-object">object</em></span><span class="dr-json-description">DOM event object</span>
</ol>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-getBBox-section"><h3 id="Element.getBBox" class="dr-method"><i class="dr-trixie">&#160;</i>Element.getBBox(isWithoutTransform)<a href="#Element.getBBox" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3118 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3118">&#x27ad;</a></h3>
<div class="extra" id="Element.getBBox-extra"></div></div><div class="dr-method"><p>Return bounding box for a given element
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">isWithoutTransform</dt>
<dd class="dr-type"><em class="dr-type-boolean">boolean</em></dd>
<dd class="dr-description">flag, <code>true</code> if you want to have bounding box before transformations. Default is <code>false</code>.</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Bounding box object:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">top left corner x</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">top left corner y</span>
<li><span class="dr-json-key">width:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">width</span>
<li><span class="dr-json-key">height:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">height</span>
</ol></li><li>}</li></ol>
</div><div class="Element-getPointAtLength-section"><h3 id="Element.getPointAtLength" class="dr-method"><i class="dr-trixie">&#160;</i>Element.getPointAtLength(length)<a href="#Element.getPointAtLength" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3370 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3370">&#x27ad;</a></h3>
<div class="extra" id="Element.getPointAtLength-extra"></div></div><div class="dr-method"><p>Return coordinates of the point located at the given length on the given path. Only works for element of “path” type.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">length</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">representation of the point:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x coordinate</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y coordinate</span>
<li><span class="dr-json-key">alpha:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">angle of derivative</span>
</ol></li><li>}</li></ol>
</div><div class="Element-getSubpath-section"><h3 id="Element.getSubpath" class="dr-method"><i class="dr-trixie">&#160;</i>Element.getSubpath(from, to)<a href="#Element.getSubpath" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3387 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3387">&#x27ad;</a></h3>
<div class="extra" id="Element.getSubpath-extra"></div></div><div class="dr-method"><p>Return subpath of a given element from given length to given length. Only works for element of “path” type.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">from</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">position of the start of the segment</dd>
<dt class="dr-param">to</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">position of the end of the segment</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">pathstring for the segment</span></p>
</div><div class="Element-getTotalLength-section"><h3 id="Element.getTotalLength" class="dr-method"><i class="dr-trixie">&#160;</i>Element.getTotalLength()<a href="#Element.getTotalLength" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3346 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3346">&#x27ad;</a></h3>
<div class="extra" id="Element.getTotalLength-extra"></div></div><div class="dr-method"><p>Returns length of the path in pixels. Only works for element of “path” type.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">length.</span></p>
</div><div class="Element-glow-section"><h3 id="Element.glow" class="dr-method"><i class="dr-trixie">&#160;</i>Element.glow([glow])<a href="#Element.glow" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3179 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3179">&#x27ad;</a></h3>
<div class="extra" id="Element.glow-extra"></div></div><div class="dr-method"><p>Return set of elements that create glow-like effect around given element. See <a href="#Paper.set" class="dr-link">Paper.set</a>.
</p>
<p>Note: Glow is not connected to the element. If you change element attributes it won’t adjust itself.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">glow</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">parameters object with all properties optional:</dd>
</dl>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">width</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">size of the glow, default is <code>10</code></span>
<li><span class="dr-json-key">fill</span><span class="dr-type"><em class="dr-type-boolean">boolean</em></span><span class="dr-json-description">will it be filled, default is <code>false</code></span>
<li><span class="dr-json-key">opacity</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">opacity, default is <code>0.5</code></span>
<li><span class="dr-json-key">offsetx</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">horizontal offset, default is <code>0</code></span>
<li><span class="dr-json-key">offsety</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">vertical offset, default is <code>0</code></span>
<li><span class="dr-json-key">color</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">glow colour, default is <code>black</code></span>
</ol></li><li>}</li></ol>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Paper.set" class="dr-link">Paper.set</a> of elements that represents glow</span></p>
</div><div class="Element-hide-section"><h3 id="Element.hide" class="dr-method"><i class="dr-trixie">&#160;</i>Element.hide()<a href="#Element.hide" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 846 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L846">&#x27ad;</a></h3>
<div class="extra" id="Element.hide-extra"></div></div><div class="dr-method"><p>Makes element invisible. See <a href="#Element.show" class="dr-link">Element.show</a>.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-hover-section"><h3 id="Element.hover" class="dr-method"><i class="dr-trixie">&#160;</i>Element.hover(f_in, f_out, [icontext], [ocontext])<a href="#Element.hover" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2645 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2645">&#x27ad;</a></h3>
<div class="extra" id="Element.hover-extra"></div></div><div class="dr-method"><p>Adds event handlers for hover for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">f_in</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for hover in</dd>
<dt class="dr-param">f_out</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for hover out</dd>
<dt class="dr-param optional">icontext</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context for hover in handler</dd>
<dt class="dr-param optional">ocontext</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context for hover out handler</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-id-section"><h3 id="Element.id" class="dr-property"><i class="dr-trixie">&#160;</i>Element.id<a href="#Element.id" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 632 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L632">&#x27ad;</a></h3>
<div class="extra" id="Element.id-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-number">number</em><p>Unique id of the element. Especially usesful when you want to listen to events of the element, 
because all events are fired in format <code>&lt;module>.&lt;action>.&lt;id></code>. Also useful for <a href="#Paper.getById" class="dr-link">Paper.getById</a> method.
</p>
</div><div class="Element-insertAfter-section"><h3 id="Element.insertAfter" class="dr-method"><i class="dr-trixie">&#160;</i>Element.insertAfter()<a href="#Element.insertAfter" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1093 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L1093">&#x27ad;</a></h3>
<div class="extra" id="Element.insertAfter-extra"></div></div><div class="dr-method"><p>Inserts current object after the given one.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-insertBefore-section"><h3 id="Element.insertBefore" class="dr-method"><i class="dr-trixie">&#160;</i>Element.insertBefore()<a href="#Element.insertBefore" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1113 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L1113">&#x27ad;</a></h3>
<div class="extra" id="Element.insertBefore-extra"></div></div><div class="dr-method"><p>Inserts current object before the given one.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-mousedown-section"><h3 id="Element.mousedown" class="dr-method"><i class="dr-trixie">&#160;</i>Element.mousedown(handler)<a href="#Element.mousedown" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2392 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2392">&#x27ad;</a></h3>
<div class="extra" id="Element.mousedown-extra"></div></div><div class="dr-method"><p>Adds event handler for mousedown for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-mousemove-section"><h3 id="Element.mousemove" class="dr-method"><i class="dr-trixie">&#160;</i>Element.mousemove(handler)<a href="#Element.mousemove" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2411 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2411">&#x27ad;</a></h3>
<div class="extra" id="Element.mousemove-extra"></div></div><div class="dr-method"><p>Adds event handler for mousemove for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-mouseout-section"><h3 id="Element.mouseout" class="dr-method"><i class="dr-trixie">&#160;</i>Element.mouseout(handler)<a href="#Element.mouseout" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2430 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2430">&#x27ad;</a></h3>
<div class="extra" id="Element.mouseout-extra"></div></div><div class="dr-method"><p>Adds event handler for mouseout for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-mouseover-section"><h3 id="Element.mouseover" class="dr-method"><i class="dr-trixie">&#160;</i>Element.mouseover(handler)<a href="#Element.mouseover" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2449 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2449">&#x27ad;</a></h3>
<div class="extra" id="Element.mouseover-extra"></div></div><div class="dr-method"><p>Adds event handler for mouseover for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-mouseup-section"><h3 id="Element.mouseup" class="dr-method"><i class="dr-trixie">&#160;</i>Element.mouseup(handler)<a href="#Element.mouseup" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2468 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2468">&#x27ad;</a></h3>
<div class="extra" id="Element.mouseup-extra"></div></div><div class="dr-method"><p>Adds event handler for mouseup for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-next-section"><h3 id="Element.next" class="dr-property"><i class="dr-trixie">&#160;</i>Element.next<a href="#Element.next" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 675 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L675">&#x27ad;</a></h3>
<div class="extra" id="Element.next-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>Reference to the next element in the hierarchy.
</p>
</div><div class="Element-node-section"><h3 id="Element.node" class="dr-property"><i class="dr-trixie">&#160;</i>Element.node<a href="#Element.node" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 611 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L611">&#x27ad;</a></h3>
<div class="extra" id="Element.node-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>Gives you a reference to the DOM object, so you can assign event handlers or just mess around.
Note: Don’t mess with it.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><span class="c">// draw a circle at coordinate <span class="d">10</span>,<span class="d">10</span> <b>with</b> radius of <span class="d">10</span></span>
<b>var</b> c<span class="s"> = </span>paper.circle(<span class="d">10</span>, <span class="d">10</span>, <span class="d">10</span>);
c.node.onclick<span class="s"> = </span><b>function</b> () {
    c.attr(<i>"fill"</i>, <i>"red"</i>);
};
</code></pre>
</div><div class="Element-onDragOver-section"><h3 id="Element.onDragOver" class="dr-method"><i class="dr-trixie">&#160;</i>Element.onDragOver(f)<a href="#Element.onDragOver" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2720 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2720">&#x27ad;</a></h3>
<div class="extra" id="Element.onDragOver-extra"></div></div><div class="dr-method"><p>Shortcut for assigning event handler for <code>drag.over.&lt;id></code> event, where id is id of the element (see <a href="#Element.id" class="dr-link">Element.id</a>).
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">f</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for event, first argument would be the element you are dragging over</dd>
</dl>
</div><div class="Element-paper-section"><h3 id="Element.paper" class="dr-property"><i class="dr-trixie">&#160;</i>Element.paper<a href="#Element.paper" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 648 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L648">&#x27ad;</a></h3>
<div class="extra" id="Element.paper-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>Internal reference to “paper” where object drawn. Mainly for use in plugins and element extensions.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>Raphael.el.cross<span class="s"> = </span><b>function</b> () {
    <b>this</b>.attr({fill: <i>"red"</i>});
    <b>this</b>.paper.path(<i>"M10,10L50,50M50,10L10,<span class="d">50</span>"</i>)
        .attr({stroke: <i>"red"</i>});
}
</code></pre>
</div><div class="Element-pause-section"><h3 id="Element.pause" class="dr-method"><i class="dr-trixie">&#160;</i>Element.pause([anim])<a href="#Element.pause" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4119 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4119">&#x27ad;</a></h3>
<div class="extra" id="Element.pause-extra"></div></div><div class="dr-method"><p>Stops animation of the element with ability to resume it later on.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">anim</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation object</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element</span></p>
</div><div class="Element-prev-section"><h3 id="Element.prev" class="dr-property"><i class="dr-trixie">&#160;</i>Element.prev<a href="#Element.prev" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 666 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L666">&#x27ad;</a></h3>
<div class="extra" id="Element.prev-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>Reference to the previous element in the hierarchy.
</p>
</div><div class="Element-raphael-section"><h3 id="Element.raphael" class="dr-property"><i class="dr-trixie">&#160;</i>Element.raphael<a href="#Element.raphael" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 624 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L624">&#x27ad;</a></h3>
<div class="extra" id="Element.raphael-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>Internal reference to <a href="#Raphael" class="dr-link">Raphael</a> object. In case it is not available.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>Raphael.el.red<span class="s"> = </span><b>function</b> () {
    <b>var</b> hsb<span class="s"> = </span><b>this</b>.paper.raphael.rgb2hsb(<b>this</b>.attr(<i>"fill"</i>));
    hsb.h<span class="s"> = </span><span class="d">1</span>;
    <b>this</b>.attr({fill: <b>this</b>.paper.raphael.hsb2rgb(hsb).hex});
}
</code></pre>
</div><div class="Element-remove-section"><h3 id="Element.remove" class="dr-method"><i class="dr-trixie">&#160;</i>Element.remove()<a href="#Element.remove" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 867 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L867">&#x27ad;</a></h3>
<div class="extra" id="Element.remove-extra"></div></div><div class="dr-method"><p>Removes element form the paper.
</p>
</div><div class="Element-removeData-section"><h3 id="Element.removeData" class="dr-method"><i class="dr-trixie">&#160;</i>Element.removeData([key])<a href="#Element.removeData" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2625 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2625">&#x27ad;</a></h3>
<div class="extra" id="Element.removeData-extra"></div></div><div class="dr-method"><p>Removes value associated with an element by given key.
If key is not provided, removes all the data of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">key</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">key</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-resume-section"><h3 id="Element.resume" class="dr-method"><i class="dr-trixie">&#160;</i>Element.resume([anim])<a href="#Element.resume" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4139 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4139">&#x27ad;</a></h3>
<div class="extra" id="Element.resume-extra"></div></div><div class="dr-method"><p>Resumes animation if it was paused with <a href="#Element.pause" class="dr-link">Element.pause</a> method.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">anim</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation object</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element</span></p>
</div><div class="Element-rotate-section"><h3 id="Element.rotate" class="dr-method"><i class="dr-trixie">&#160;</i>Element.rotate(deg, [cx], [cy])<a href="#Element.rotate" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 707 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L707">&#x27ad;</a></h3>
<div class="extra" id="Element.rotate-extra"></div></div><div class="dr-method"><p>Adds rotation by given angle around given point to the list of
transformations of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">deg</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">angle in degrees</dd>
<dt class="dr-param optional">cx</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate of the centre of rotation</dd>
<dt class="dr-param optional">cy</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate of the centre of rotation</dd>
</dl>
<p>If cx <em class="amp">&amp;</em> cy aren’t specified centre of the shape is used as a point of rotation.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-scale-section"><h3 id="Element.scale" class="dr-method"><i class="dr-trixie">&#160;</i>Element.scale(sx, sy, [cx], [cy])<a href="#Element.scale" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 740 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L740">&#x27ad;</a></h3>
<div class="extra" id="Element.scale-extra"></div></div><div class="dr-method"><p>Adds scale by given amount relative to given point to the list of
transformations of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">sx</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">horisontal scale amount</dd>
<dt class="dr-param">sy</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">vertical scale amount</dd>
<dt class="dr-param optional">cx</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate of the centre of scale</dd>
<dt class="dr-param optional">cy</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate of the centre of scale</dd>
</dl>
<p>If cx <em class="amp">&amp;</em> cy aren’t specified centre of the shape is used instead.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-setTime-section"><h3 id="Element.setTime" class="dr-method"><i class="dr-trixie">&#160;</i>Element.setTime(anim, value)<a href="#Element.setTime" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4052 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4052">&#x27ad;</a></h3>
<div class="extra" id="Element.setTime-extra"></div></div><div class="dr-method"><p>Sets the status of animation of the element in milliseconds. Similar to <a href="#Element.status" class="dr-link">Element.status</a> method.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">anim</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation object</dd>
<dt class="dr-param">value</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number of milliseconds from the beginning of the animation</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element if <code>value</code> is specified</span></p>
<p>Note, that during animation following events are triggered:
</p>
<p>On each animation frame event <code>anim.frame.&lt;id></code>, on start <code>anim.start.&lt;id></code> and on end <code>anim.finish.&lt;id></code>.
</p>
</div><div class="Element-show-section"><h3 id="Element.show" class="dr-method"><i class="dr-trixie">&#160;</i>Element.show()<a href="#Element.show" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 857 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L857">&#x27ad;</a></h3>
<div class="extra" id="Element.show-extra"></div></div><div class="dr-method"><p>Makes element visible. See <a href="#Element.hide" class="dr-link">Element.hide</a>.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-status-section"><h3 id="Element.status" class="dr-method"><i class="dr-trixie">&#160;</i>Element.status([anim], [value])<a href="#Element.status" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4079 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4079">&#x27ad;</a></h3>
<div class="extra" id="Element.status-extra"></div></div><div class="dr-method"><p>Gets or sets the status of animation of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">anim</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation object</dd>
<dt class="dr-param optional">value</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">0 – 1. If specified, method works like a setter and sets the status of a given animation to the value. This will cause animation to jump to the given position.</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">status</span></p>
<p>or
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-array">array</em> <span class="dr-description">status if <code>anim</code> is not specified. Array of objects in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">anim:</span><span class="dr-type"><em class="dr-type-object">object</em></span><span class="dr-json-description">animation object</span>
<li><span class="dr-json-key">status:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">status</span>
</ol></li><li>}</li></ol>
<p>or
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element if <code>value</code> is specified</span></p>
</div><div class="Element-stop-section"><h3 id="Element.stop" class="dr-method"><i class="dr-trixie">&#160;</i>Element.stop([anim])<a href="#Element.stop" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4161 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4161">&#x27ad;</a></h3>
<div class="extra" id="Element.stop-extra"></div></div><div class="dr-method"><p>Stops animation of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">anim</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">animation object</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element</span></p>
</div><div class="Element-toBack-section"><h3 id="Element.toBack" class="dr-method"><i class="dr-trixie">&#160;</i>Element.toBack()<a href="#Element.toBack" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1072 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L1072">&#x27ad;</a></h3>
<div class="extra" id="Element.toBack-extra"></div></div><div class="dr-method"><p>Moves the element so it is the furthest from the viewer’s eyes, behind other elements.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-toFront-section"><h3 id="Element.toFront" class="dr-method"><i class="dr-trixie">&#160;</i>Element.toFront()<a href="#Element.toFront" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1052 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L1052">&#x27ad;</a></h3>
<div class="extra" id="Element.toFront-extra"></div></div><div class="dr-method"><p>Moves the element so it is the closest to the viewer’s eyes, on top of other elements.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-touchcancel-section"><h3 id="Element.touchcancel" class="dr-method"><i class="dr-trixie">&#160;</i>Element.touchcancel(handler)<a href="#Element.touchcancel" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2544 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2544">&#x27ad;</a></h3>
<div class="extra" id="Element.touchcancel-extra"></div></div><div class="dr-method"><p>Adds event handler for touchcancel for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-touchend-section"><h3 id="Element.touchend" class="dr-method"><i class="dr-trixie">&#160;</i>Element.touchend(handler)<a href="#Element.touchend" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2525 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2525">&#x27ad;</a></h3>
<div class="extra" id="Element.touchend-extra"></div></div><div class="dr-method"><p>Adds event handler for touchend for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-touchmove-section"><h3 id="Element.touchmove" class="dr-method"><i class="dr-trixie">&#160;</i>Element.touchmove(handler)<a href="#Element.touchmove" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2506 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2506">&#x27ad;</a></h3>
<div class="extra" id="Element.touchmove-extra"></div></div><div class="dr-method"><p>Adds event handler for touchmove for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-touchstart-section"><h3 id="Element.touchstart" class="dr-method"><i class="dr-trixie">&#160;</i>Element.touchstart(handler)<a href="#Element.touchstart" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2487 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2487">&#x27ad;</a></h3>
<div class="extra" id="Element.touchstart-extra"></div></div><div class="dr-method"><p>Adds event handler for touchstart for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-transform-section"><h3 id="Element.transform" class="dr-method"><i class="dr-trixie">&#160;</i>Element.transform([tstr])<a href="#Element.transform" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 821 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L821">&#x27ad;</a></h3>
<div class="extra" id="Element.transform-extra"></div></div><div class="dr-method"><p>Adds transformation to the element which is separate to other attributes,
i.e. translation doesn’t change <code>x</code> or <code>y</code> of the rectange. The format
of transformation string is similar to the path string syntax:
</p>
<pre class="javascript code"><code><i>"t100,100r30,<span class="d">100</span>,100s2,<span class="d">2</span>,<span class="d">100</span>,100r45s1<span class="d">.5</span>"</i>
</code></pre>
<p>Each letter is a command. There are four commands: <code>t</code> is for translate, <code>r</code> is for rotate, <code>s</code> is for
scale and <code>m</code> is for matrix.
</p>
<p>There are also alternative “absolute” translation, rotation and scale: <code>T</code>, <code>R</code> and <code>S</code>. They will not take previous transformation into account. For example, <code>...T100,0</code> will always move element 100 px horisontally, while <code>...t100,0</code> could move it vertically if there is <code>r90</code> before. Just compare results of <code>r90t100,0</code> and <code>r90T100,0</code>.
</p>
<p>So, the example line above could be read like “translate by 100, 100; rotate 30° around 100, 100; scale twice around 100, 100;
rotate 45° around centre; scale 1.5 times relative to centre”. As you can see rotate and scale commands have origin
coordinates as optional parameters, the default is the centre point of the element.
Matrix accepts six parameters.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> el<span class="s"> = </span>paper.rect(<span class="d">10</span>, <span class="d">20</span>, <span class="d">300</span>, <span class="d">200</span>);
<span class="c">// translate <span class="d">100</span>, <span class="d">100</span>, rotate <span class="d">45</span>°, translate -<span class="d">100</span>, <span class="d">0</span></span>
el.transform(<i>"t100,100r45t-<span class="d">100</span>,<span class="d">0</span>"</i>);
<span class="c">// <b>if</b> you want you can append or prepend transformations</span>
el.transform(<i>"...t50,<span class="d">50</span>"</i>);
el.transform(<i>"s2..."</i>);
<span class="c">// or even wrap</span>
el.transform(<i>"t50,<span class="d">50</span>...t-<span class="d">50</span>-<span class="d">50</span>"</i>);
<span class="c">// to reset transformation call method <b>with</b> empty string</span>
el.transform(<i>""</i>);
<span class="c">// to get current value call it without parameters</span>
console.log(el.transform());
</code></pre>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">tstr</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">transformation string</dd>
</dl>
<p>If tstr isn’t specified
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">current transformation string</span></p>
<p>else
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-translate-section"><h3 id="Element.translate" class="dr-method"><i class="dr-trixie">&#160;</i>Element.translate(dx, dy)<a href="#Element.translate" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 771 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L771">&#x27ad;</a></h3>
<div class="extra" id="Element.translate-extra"></div></div><div class="dr-method"><p>Adds translation by given amount to the list of transformations of the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">dx</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">horisontal shift</dd>
<dt class="dr-param">dy</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">vertical shift</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-unclick-section"><h3 id="Element.unclick" class="dr-method"><i class="dr-trixie">&#160;</i>Element.unclick(handler)<a href="#Element.unclick" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2363 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2363">&#x27ad;</a></h3>
<div class="extra" id="Element.unclick-extra"></div></div><div class="dr-method"><p>Removes event handler for click for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-undblclick-section"><h3 id="Element.undblclick" class="dr-method"><i class="dr-trixie">&#160;</i>Element.undblclick(handler)<a href="#Element.undblclick" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2382 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2382">&#x27ad;</a></h3>
<div class="extra" id="Element.undblclick-extra"></div></div><div class="dr-method"><p>Removes event handler for double click for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-undrag-section"><h3 id="Element.undrag" class="dr-method"><i class="dr-trixie">&#160;</i>Element.undrag()<a href="#Element.undrag" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2729 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2729">&#x27ad;</a></h3>
<div class="extra" id="Element.undrag-extra"></div></div><div class="dr-method"><p>Removes all drag event handlers from given element.
</p>
</div><div class="Element-unhover-section"><h3 id="Element.unhover" class="dr-method"><i class="dr-trixie">&#160;</i>Element.unhover(f_in, f_out)<a href="#Element.unhover" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2658 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2658">&#x27ad;</a></h3>
<div class="extra" id="Element.unhover-extra"></div></div><div class="dr-method"><p>Removes event handlers for hover for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">f_in</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for hover in</dd>
<dt class="dr-param">f_out</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for hover out</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-unmousedown-section"><h3 id="Element.unmousedown" class="dr-method"><i class="dr-trixie">&#160;</i>Element.unmousedown(handler)<a href="#Element.unmousedown" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2401 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2401">&#x27ad;</a></h3>
<div class="extra" id="Element.unmousedown-extra"></div></div><div class="dr-method"><p>Removes event handler for mousedown for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-unmousemove-section"><h3 id="Element.unmousemove" class="dr-method"><i class="dr-trixie">&#160;</i>Element.unmousemove(handler)<a href="#Element.unmousemove" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2420 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2420">&#x27ad;</a></h3>
<div class="extra" id="Element.unmousemove-extra"></div></div><div class="dr-method"><p>Removes event handler for mousemove for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-unmouseout-section"><h3 id="Element.unmouseout" class="dr-method"><i class="dr-trixie">&#160;</i>Element.unmouseout(handler)<a href="#Element.unmouseout" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2439 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2439">&#x27ad;</a></h3>
<div class="extra" id="Element.unmouseout-extra"></div></div><div class="dr-method"><p>Removes event handler for mouseout for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-unmouseover-section"><h3 id="Element.unmouseover" class="dr-method"><i class="dr-trixie">&#160;</i>Element.unmouseover(handler)<a href="#Element.unmouseover" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2458 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2458">&#x27ad;</a></h3>
<div class="extra" id="Element.unmouseover-extra"></div></div><div class="dr-method"><p>Removes event handler for mouseover for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-unmouseup-section"><h3 id="Element.unmouseup" class="dr-method"><i class="dr-trixie">&#160;</i>Element.unmouseup(handler)<a href="#Element.unmouseup" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2477 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2477">&#x27ad;</a></h3>
<div class="extra" id="Element.unmouseup-extra"></div></div><div class="dr-method"><p>Removes event handler for mouseup for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-untouchcancel-section"><h3 id="Element.untouchcancel" class="dr-method"><i class="dr-trixie">&#160;</i>Element.untouchcancel(handler)<a href="#Element.untouchcancel" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2553 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2553">&#x27ad;</a></h3>
<div class="extra" id="Element.untouchcancel-extra"></div></div><div class="dr-method"><p>Removes event handler for touchcancel for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-untouchend-section"><h3 id="Element.untouchend" class="dr-method"><i class="dr-trixie">&#160;</i>Element.untouchend(handler)<a href="#Element.untouchend" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2534 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2534">&#x27ad;</a></h3>
<div class="extra" id="Element.untouchend-extra"></div></div><div class="dr-method"><p>Removes event handler for touchend for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-untouchmove-section"><h3 id="Element.untouchmove" class="dr-method"><i class="dr-trixie">&#160;</i>Element.untouchmove(handler)<a href="#Element.untouchmove" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2515 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2515">&#x27ad;</a></h3>
<div class="extra" id="Element.untouchmove-extra"></div></div><div class="dr-method"><p>Removes event handler for touchmove for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Element-untouchstart-section"><h3 id="Element.untouchstart" class="dr-method"><i class="dr-trixie">&#160;</i>Element.untouchstart(handler)<a href="#Element.untouchstart" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2496 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2496">&#x27ad;</a></h3>
<div class="extra" id="Element.untouchstart-extra"></div></div><div class="dr-method"><p>Removes event handler for touchstart for the element.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">handler</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">handler for the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Element" class="dr-link">Element</a></span></p>
</div><div class="Matrix-section"><h2 id="Matrix" class="undefined"><i class="dr-trixie">&#160;</i>Matrix<a href="#Matrix" title="Link to this section" class="dr-hash">&#x2693;</a></h2>
<div class="extra" id="Matrix-extra"></div></div><div class="Matrix-add-section"><h3 id="Matrix.add" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.add(a, b, c, d, e, f, matrix)<a href="#Matrix.add" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1979 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L1979">&#x27ad;</a></h3>
<div class="extra" id="Matrix.add-extra"></div></div><div class="dr-method"><p>Adds given matrix to existing one.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">a</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">b</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">c</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">d</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">e</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">f</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">matrix</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description"><a href="#Matrix" class="dr-link">Matrix</a></dd>
</dl>
</div><div class="Matrix-clone-section"><h3 id="Matrix.clone" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.clone()<a href="#Matrix.clone" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2024 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2024">&#x27ad;</a></h3>
<div class="extra" id="Matrix.clone-extra"></div></div><div class="dr-method"><p>Returns copy of the matrix
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Matrix" class="dr-link">Matrix</a></span></p>
</div><div class="Matrix-invert-section"><h3 id="Matrix.invert" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.invert()<a href="#Matrix.invert" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2012 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2012">&#x27ad;</a></h3>
<div class="extra" id="Matrix.invert-extra"></div></div><div class="dr-method"><p>Returns inverted version of the matrix
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Matrix" class="dr-link">Matrix</a></span></p>
</div><div class="Matrix-rotate-section"><h3 id="Matrix.rotate" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.rotate(a, x, y)<a href="#Matrix.rotate" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2066 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2066">&#x27ad;</a></h3>
<div class="extra" id="Matrix.rotate-extra"></div></div><div class="dr-method"><p>Rotates the matrix
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">a</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
</div><div class="Matrix-scale-section"><h3 id="Matrix.scale" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.scale(x, [y], [cx], [cy])<a href="#Matrix.scale" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2050 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2050">&#x27ad;</a></h3>
<div class="extra" id="Matrix.scale-extra"></div></div><div class="dr-method"><p>Scales the matrix
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param optional">y</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param optional">cx</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param optional">cy</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
</div><div class="Matrix-split-section"><h3 id="Matrix.split" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.split()<a href="#Matrix.split" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2139 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2139">&#x27ad;</a></h3>
<div class="extra" id="Matrix.split-extra"></div></div><div class="dr-method"><p>Splits matrix into primitive transformations
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">in format:</span></p>
<ol class="dr-json"><li><span class="dr-json-key">dx</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">translation by x</span>
<li><span class="dr-json-key">dy</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">translation by y</span>
<li><span class="dr-json-key">scalex</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">scale by x</span>
<li><span class="dr-json-key">scaley</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">scale by y</span>
<li><span class="dr-json-key">shear</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">shear</span>
<li><span class="dr-json-key">rotate</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">rotation in deg</span>
<li><span class="dr-json-key">isSimple</span><span class="dr-type"><em class="dr-type-boolean">boolean</em></span><span class="dr-json-description">could it be represented via simple transformations</span>
</ol>
</div><div class="Matrix-toTransformString-section"><h3 id="Matrix.toTransformString" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.toTransformString()<a href="#Matrix.toTransformString" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2181 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2181">&#x27ad;</a></h3>
<div class="extra" id="Matrix.toTransformString-extra"></div></div><div class="dr-method"><p>Return transform string that represents given matrix
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">transform string</span></p>
</div><div class="Matrix-translate-section"><h3 id="Matrix.translate" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.translate(x, y)<a href="#Matrix.translate" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2036 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2036">&#x27ad;</a></h3>
<div class="extra" id="Matrix.translate-extra"></div></div><div class="dr-method"><p>Translate the matrix
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
</div><div class="Matrix-x-section"><h3 id="Matrix.x" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.x(x, y)<a href="#Matrix.x" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2085 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2085">&#x27ad;</a></h3>
<div class="extra" id="Matrix.x-extra"></div></div><div class="dr-method"><p>Return x coordinate for given point after transformation described by the matrix. See also <a href="#Matrix.y" class="dr-link">Matrix.y</a>
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">x</span></p>
</div><div class="Matrix-y-section"><h3 id="Matrix.y" class="dr-method"><i class="dr-trixie">&#160;</i>Matrix.y(x, y)<a href="#Matrix.y" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2098 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2098">&#x27ad;</a></h3>
<div class="extra" id="Matrix.y-extra"></div></div><div class="dr-method"><p>Return y coordinate for given point after transformation described by the matrix. See also <a href="#Matrix.x" class="dr-link">Matrix.x</a>
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">y</span></p>
</div><div class="Paper-section"><h2 id="Paper" class="undefined"><i class="dr-trixie">&#160;</i>Paper<a href="#Paper" title="Link to this section" class="dr-hash">&#x2693;</a></h2>
<div class="extra" id="Paper-extra"></div></div><div class="Paper-add-section"><h3 id="Paper.add" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.add(json)<a href="#Paper.add" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4567 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4567">&#x27ad;</a></h3>
<div class="extra" id="Paper.add-extra"></div></div><div class="dr-method"><p>Imports elements in JSON array in format <code>{type: type, &lt;attributes>}</code>
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">json</dt>
<dd class="dr-type"><em class="dr-type-array">array</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">resulting set of imported elements</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>paper.add([
    {
        type: <i>"circle"</i>,
        cx: <span class="d">10</span>,
        cy: <span class="d">10</span>,
        r: <span class="d">5</span>
    },
    {
        type: <i>"rect"</i>,
        x: <span class="d">10</span>,
        y: <span class="d">10</span>,
        width: <span class="d">10</span>,
        height: <span class="d">10</span>,
        fill: <i>"#fc0"</i>
    }
]);
</code></pre>
</div><div class="Paper-bottom-section"><h3 id="Paper.bottom" class="dr-property"><i class="dr-trixie">&#160;</i>Paper.bottom<a href="#Paper.bottom" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2986 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2986">&#x27ad;</a></h3>
<div class="extra" id="Paper.bottom-extra"></div></div><div class="dr-property"><p>Points to the bottom element on the paper
</p>
</div><div class="Paper-ca-section"><h3 id="Paper.ca" class="dr-property"><i class="dr-trixie">&#160;</i>Paper.ca<a href="#Paper.ca" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 99 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L99">&#x27ad;</a></h3>
<div class="extra" id="Paper.ca-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>Shortcut for <a href="#Paper.customAttributes" class="dr-link">Paper.customAttributes</a>
</p>
</div><div class="Paper-circle-section"><h3 id="Paper.circle" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.circle(x, y, r)<a href="#Paper.circle" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2754 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2754">&#x27ad;</a></h3>
<div class="extra" id="Paper.circle-extra"></div></div><div class="dr-method"><p>Draws a circle.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate of the centre</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate of the centre</dd>
<dt class="dr-param">r</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">radius</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphaël element object with type “circle”</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> c<span class="s"> = </span>paper.circle(<span class="d">50</span>, <span class="d">50</span>, <span class="d">40</span>);
</code></pre>
</div><div class="Paper-clear-section"><h3 id="Paper.clear" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.clear()<a href="#Paper.clear" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1322 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L1322">&#x27ad;</a></h3>
<div class="extra" id="Paper.clear-extra"></div></div><div class="dr-method"><p>Clears the paper, i.e. removes all the elements.
</p>
</div><div class="Paper-customAttributes-section"><h3 id="Paper.customAttributes" class="dr-property"><i class="dr-trixie">&#160;</i>Paper.customAttributes<a href="#Paper.customAttributes" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 125 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L125">&#x27ad;</a></h3>
<div class="extra" id="Paper.customAttributes-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>If you have a set of attributes that you would like to represent
as a function of some number you can do it easily with custom attributes:
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>paper.customAttributes.hue<span class="s"> = </span><b>function</b> (num) {
    num<span class="s"> = </span>num<span class="s"> % </span><span class="d">1</span>;
    <b>return</b> {fill: <i>"hsb("</i><span class="s"> + </span>num<span class="s"> + </span><i>", <span class="d">0.75</span>, <span class="d">1</span>)"</i>};
};
<span class="c">// Custom attribute “hue” will change fill</span>
<span class="c">// to be given hue <b>with</b> fixed saturation and brightness.</span>
<span class="c">// Now you can use it like <b>this</b>:</span>
<b>var</b> c<span class="s"> = </span>paper.circle(<span class="d">10</span>, <span class="d">10</span>, <span class="d">10</span>).attr({hue: <span class="d">.45</span>});
<span class="c">// or even like <b>this</b>:</span>
c.animate({hue: <span class="d">1</span>}, <span class="d">1e3</span>);

<span class="c">// You could also create custom attribute</span>
<span class="c">// <b>with</b> multiple parameters:</span>
paper.customAttributes.hsb<span class="s"> = </span><b>function</b> (h, s, b) {
    <b>return</b> {fill: <i>"hsb("</i><span class="s"> + </span>[h, s, b].join(<i>","</i>)<span class="s"> + </span><i>")"</i>};
};
c.attr({hsb: <i>"<span class="d">0.5</span> <span class="d">.8</span> <span class="d">1</span>"</i>});
c.animate({hsb: [<span class="d">1</span>, <span class="d">0</span>, <span class="d">0.5</span>]}, <span class="d">1e3</span>);
</code></pre>
</div><div class="Paper-ellipse-section"><h3 id="Paper.ellipse" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.ellipse(x, y, rx, ry)<a href="#Paper.ellipse" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2802 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2802">&#x27ad;</a></h3>
<div class="extra" id="Paper.ellipse-extra"></div></div><div class="dr-method"><p>Draws an ellipse.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate of the centre</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate of the centre</dd>
<dt class="dr-param">rx</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">horizontal radius</dd>
<dt class="dr-param">ry</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">vertical radius</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphaël element object with type “ellipse”</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> c<span class="s"> = </span>paper.ellipse(<span class="d">50</span>, <span class="d">50</span>, <span class="d">40</span>, <span class="d">20</span>);
</code></pre>
</div><div class="Paper-forEach-section"><h3 id="Paper.forEach" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.forEach(callback, thisArg)<a href="#Paper.forEach" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3085 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3085">&#x27ad;</a></h3>
<div class="extra" id="Paper.forEach-extra"></div></div><div class="dr-method"><p>Executes given function for each element on the paper
</p>
<p>If callback function returns <code>false</code> it will stop loop running.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">callback</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">function to run</dd>
<dt class="dr-param">thisArg</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context object for the callback</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Paper object</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>paper.forEach(<b>function</b> (el) {
    el.attr({ stroke: <i>"blue"</i> });
});
</code></pre>
</div><div class="Paper-getById-section"><h3 id="Paper.getById" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.getById(id)<a href="#Paper.getById" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3057 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3057">&#x27ad;</a></h3>
<div class="extra" id="Paper.getById-extra"></div></div><div class="dr-method"><p>Returns you element by its internal ID.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">id</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">id</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphaël element object</span></p>
</div><div class="Paper-getElementByPoint-section"><h3 id="Paper.getElementByPoint" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.getElementByPoint(x, y)<a href="#Paper.getElementByPoint" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3021 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3021">&#x27ad;</a></h3>
<div class="extra" id="Paper.getElementByPoint-extra"></div></div><div class="dr-method"><p>Returns you topmost element under given point.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphaël element object</span></p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate from the top left corner of the window</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate from the top left corner of the window</dd>
</dl>
<p class="header">Usage
</p>
<pre class="javascript code"><code>paper.getElementByPoint(mouseX, mouseY).attr({stroke: <i>"#f00"</i>});
</code></pre>
</div><div class="Paper-getFont-section"><h3 id="Paper.getFont" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.getFont(family, [weight], [style], [stretch])<a href="#Paper.getFont" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4460 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4460">&#x27ad;</a></h3>
<div class="extra" id="Paper.getFont-extra"></div></div><div class="dr-method"><p>Finds font object in the registered fonts by given parameters. You could specify only one word from the font name, like “Myriad” for “Myriad Pro”.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">family</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">font family name or any word from it</dd>
<dt class="dr-param optional">weight</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">font weight</dd>
<dt class="dr-param optional">style</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">font style</dd>
<dt class="dr-param optional">stretch</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">font stretch</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">the font object</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>paper.print(<span class="d">100</span>, <span class="d">100</span>, <i>"Test string"</i>, paper.getFont(<i>"Times"</i>, <span class="d">800</span>), <span class="d">30</span>);
</code></pre>
</div><div class="Paper-image-section"><h3 id="Paper.image" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.image(src, x, y, width, height)<a href="#Paper.image" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2863 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2863">&#x27ad;</a></h3>
<div class="extra" id="Paper.image-extra"></div></div><div class="dr-method"><p>Embeds an image into the surface.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">src</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">URI of the source image</dd>
<dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate position</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate position</dd>
<dt class="dr-param">width</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">width of the image</dd>
<dt class="dr-param">height</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">height of the image</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphaël element object with type “image”</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> c<span class="s"> = </span>paper.image(<i>"apple.png"</i>, <span class="d">10</span>, <span class="d">10</span>, <span class="d">80</span>, <span class="d">80</span>);
</code></pre>
</div><div class="Paper-path-section"><h3 id="Paper.path" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.path([pathString])<a href="#Paper.path" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2839 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2839">&#x27ad;</a></h3>
<div class="extra" id="Paper.path-extra"></div></div><div class="dr-method"><p>Creates a path element by given path data string.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">pathString</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">path string in SVG format.</dd>
</dl>
<p>Path string consists of one-letter commands, followed by comma seprarated arguments in numercal form. Example:
</p>
<pre class="javascript code"><code><i>"M10,20L30,<span class="d">40</span>"</i>
</code></pre>
<p>Here we can see two commands: “M”, with arguments <code>(10, 20)</code> and “L” with arguments <code>(30, 40)</code>. Upper case letter mean command is absolute, lower case—relative.
</p>
<p></p>
<p>Here is short list of commands available, for more details see <a href="http://www.w3.org/TR/SVG/paths.html#PathData" title="Details of a path's data attribute's format are described in the SVG specification.">SVG path string format</a>.</p>
<table><thead><tr><th>Command</th><th>Name</th><th>Parameters</th></tr></thead><tbody>
<tr><td>M</td><td>moveto</td><td>(x y)+</td></tr>
<tr><td>Z</td><td>closepath</td><td>(none)</td></tr>
<tr><td>L</td><td>lineto</td><td>(x y)+</td></tr>
<tr><td>H</td><td>horizontal lineto</td><td>x+</td></tr>
<tr><td>V</td><td>vertical lineto</td><td>y+</td></tr>
<tr><td>C</td><td>curveto</td><td>(x1 y1 x2 y2 x y)+</td></tr>
<tr><td>S</td><td>smooth curveto</td><td>(x2 y2 x y)+</td></tr>
<tr><td>Q</td><td>quadratic Bézier curveto</td><td>(x1 y1 x y)+</td></tr>
<tr><td>T</td><td>smooth quadratic Bézier curveto</td><td>(x y)+</td></tr>
<tr><td>A</td><td>elliptical arc</td><td>(rx ry x-axis-rotation large-arc-flag sweep-flag x y)+</td></tr>
<tr><td>R</td><td><a href="http://en.wikipedia.org/wiki/Catmull–Rom_spline#Catmull.E2.80.93Rom_spline">Catmull-Rom curveto</a>*</td><td>x1 y1 (x y)+</td></tr></tbody></table>
<p>* “Catmull-Rom curveto” is a not standard SVG command and added in 2.0 to make life easier.
Note: there is a special case when path consist of just three commands: “M10,10R…z”. In this case path will smoothly connects to its beginning.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> c<span class="s"> = </span>paper.path(<i>"M10 10L90 <span class="d">90</span>"</i>);
<span class="c">// draw a diagonal line:</span>
<span class="c">// move to <span class="d">10</span>,<span class="d">10</span>, line to <span class="d">90</span>,<span class="d">90</span></span>
</code></pre>
<p>For example of path strings, check out these icons: <a href="http://raphaeljs.com/icons/" rel="external">http://raphaeljs.com/icons/</a>
</p>
</div><div class="Paper-print-section"><h3 id="Paper.print" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.print(x, y, text, font, [size], [origin], [letter_spacing])<a href="#Paper.print" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4510 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4510">&#x27ad;</a></h3>
<div class="extra" id="Paper.print-extra"></div></div><div class="dr-method"><p>Creates set of shapes to represent given font at given position with given size.
Result of the method is set object (see <a href="#Paper.set" class="dr-link">Paper.set</a>) which contains each letter as separate path object.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x position of the text</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y position of the text</dd>
<dt class="dr-param">text</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">text to print</dd>
<dt class="dr-param">font</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">font object, see <a href="#Paper.getFont" class="dr-link">Paper.getFont</a></dd>
<dt class="dr-param optional">size</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">size of the font, default is <code>16</code></dd>
<dt class="dr-param optional">origin</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">could be <code>"baseline"</code> or <code>"middle"</code>, default is <code>"middle"</code></dd>
<dt class="dr-param optional">letter_spacing</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number in range <code>-1..1</code>, default is <code>0</code></dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">resulting set of letters</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> txt<span class="s"> = </span>r.print(<span class="d">10</span>, <span class="d">50</span>, <i>"print"</i>, r.getFont(<i>"Museo"</i>), <span class="d">30</span>).attr({fill: <i>"#fff"</i>});
<span class="c">// following line will paint first letter <b>in</b> red</span>
txt[<span class="d">0</span>].attr({fill: <i>"#f00"</i>});
</code></pre>
</div><div class="Paper-raphael-section"><h3 id="Paper.raphael" class="dr-property"><i class="dr-trixie">&#160;</i>Paper.raphael<a href="#Paper.raphael" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2993 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2993">&#x27ad;</a></h3>
<div class="extra" id="Paper.raphael-extra"></div></div><div class="dr-property"><p>Points to the <a href="#Raphael" class="dr-link">Raphael</a> object/function
</p>
</div><div class="Paper-rect-section"><h3 id="Paper.rect" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.rect(x, y, width, height, [r])<a href="#Paper.rect" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2780 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2780">&#x27ad;</a></h3>
<div class="extra" id="Paper.rect-extra"></div></div><div class="dr-method"><p></p>
<p>Draws a rectangle.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate of the top left corner</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate of the top left corner</dd>
<dt class="dr-param">width</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">width</dd>
<dt class="dr-param">height</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">height</dd>
<dt class="dr-param optional">r</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">radius for rounded corners, default is 0</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphaël element object with type “rect”</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><span class="c">// regular rectangle</span>
<b>var</b> c<span class="s"> = </span>paper.rect(<span class="d">10</span>, <span class="d">10</span>, <span class="d">50</span>, <span class="d">50</span>);
<span class="c">// rectangle <b>with</b> rounded corners</span>
<b>var</b> c<span class="s"> = </span>paper.rect(<span class="d">40</span>, <span class="d">40</span>, <span class="d">50</span>, <span class="d">50</span>, <span class="d">10</span>);
</code></pre>
</div><div class="Paper-remove-section"><h3 id="Paper.remove" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.remove()<a href="#Paper.remove" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1339 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L1339">&#x27ad;</a></h3>
<div class="extra" id="Paper.remove-extra"></div></div><div class="dr-method"><p>Removes the paper from the DOM.
</p>
</div><div class="Paper-renderfix-section"><h3 id="Paper.renderfix" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.renderfix()<a href="#Paper.renderfix" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1294 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.svg.js#L1294">&#x27ad;</a></h3>
<div class="extra" id="Paper.renderfix-extra"></div></div><div class="dr-method"><p>Fixes the issue of Firefox and IE9 regarding subpixel rendering. If paper is dependant
on other elements after reflow it could shift half pixel which cause for lines to lost their crispness.
This method fixes the issue.
</p>
</div><div class="Paper-safari-section"><h3 id="Paper.safari" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.safari()<a href="#Paper.safari" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2208 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2208">&#x27ad;</a></h3>
<div class="extra" id="Paper.safari-extra"></div></div><div class="dr-method"><p>There is an inconvenient rendering bug in Safari (WebKit):
sometimes the rendering should be forced.
This method should help with dealing with this bug.
</p>
</div><div class="Paper-set-section"><h3 id="Paper.set" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.set()<a href="#Paper.set" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2906 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2906">&#x27ad;</a></h3>
<div class="extra" id="Paper.set-extra"></div></div><div class="dr-method"><p>Creates array-like object to keep and operate several elements at once.
Warning: it doesn’t create any elements for itself in the page, it just groups existing elements.
Sets act as pseudo elements — all methods available to an element can be used on a set.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">array-like object that represents set of elements</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> st<span class="s"> = </span>paper.set();
st.push(
    paper.circle(<span class="d">10</span>, <span class="d">10</span>, <span class="d">5</span>),
    paper.circle(<span class="d">30</span>, <span class="d">10</span>, <span class="d">5</span>)
);
st.attr({fill: <i>"red"</i>}); <span class="c">// changes the fill of both circles</span>
</code></pre>
</div><div class="Paper-setFinish-section"><h3 id="Paper.setFinish" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.setFinish()<a href="#Paper.setFinish" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2937 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2937">&#x27ad;</a></h3>
<div class="extra" id="Paper.setFinish-extra"></div></div><div class="dr-method"><p>See <a href="#Paper.setStart" class="dr-link">Paper.setStart</a>. This method finishes catching and returns resulting set.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">set</span></p>
</div><div class="Paper-setSize-section"><h3 id="Paper.setSize" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.setSize(width, height)<a href="#Paper.setSize" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2953 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2953">&#x27ad;</a></h3>
<div class="extra" id="Paper.setSize-extra"></div></div><div class="dr-method"><p>If you need to change dimensions of the canvas call this method
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">width</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">new width of the canvas</dd>
<dt class="dr-param">height</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">new height of the canvas</dd>
</dl>
</div><div class="Paper-setStart-section"><h3 id="Paper.setStart" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.setStart()<a href="#Paper.setStart" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2926 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2926">&#x27ad;</a></h3>
<div class="extra" id="Paper.setStart-extra"></div></div><div class="dr-method"><p>Creates <a href="#Paper.set" class="dr-link">Paper.set</a>. All elements that will be created after calling this method and before calling
<a href="#Paper.setFinish" class="dr-link">Paper.setFinish</a> will be added to the set.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>paper.setStart();
paper.circle(<span class="d">10</span>, <span class="d">10</span>, <span class="d">5</span>),
paper.circle(<span class="d">30</span>, <span class="d">10</span>, <span class="d">5</span>)
<b>var</b> st<span class="s"> = </span>paper.setFinish();
st.attr({fill: <i>"red"</i>}); <span class="c">// changes the fill of both circles</span>
</code></pre>
</div><div class="Paper-setViewBox-section"><h3 id="Paper.setViewBox" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.setViewBox(x, y, w, h, fit)<a href="#Paper.setViewBox" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2971 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2971">&#x27ad;</a></h3>
<div class="extra" id="Paper.setViewBox-extra"></div></div><div class="dr-method"><p>Sets the view box of the paper. Practically it gives you ability to zoom and pan whole paper surface by 
specifying new boundaries.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">new x position, default is <code>0</code></dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">new y position, default is <code>0</code></dd>
<dt class="dr-param">w</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">new width of the canvas</dd>
<dt class="dr-param">h</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">new height of the canvas</dd>
<dt class="dr-param">fit</dt>
<dd class="dr-type"><em class="dr-type-boolean">boolean</em></dd>
<dd class="dr-description"><code>true</code> if you want graphics to fit into new boundary box</dd>
</dl>
</div><div class="Paper-text-section"><h3 id="Paper.text" class="dr-method"><i class="dr-trixie">&#160;</i>Paper.text(x, y, text)<a href="#Paper.text" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2884 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2884">&#x27ad;</a></h3>
<div class="extra" id="Paper.text-extra"></div></div><div class="dr-method"><p>Draws a text string. If you need line breaks, put “\n” in the string.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coordinate position</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coordinate position</dd>
<dt class="dr-param">text</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">The text string to draw</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphaël element object with type “text”</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> t<span class="s"> = </span>paper.text(<span class="d">50</span>, <span class="d">50</span>, <i>"Raphaël\nkicks\nbutt!"</i>);
</code></pre>
</div><div class="Paper-top-section"><h3 id="Paper.top" class="dr-property"><i class="dr-trixie">&#160;</i>Paper.top<a href="#Paper.top" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2980 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2980">&#x27ad;</a></h3>
<div class="extra" id="Paper.top-extra"></div></div><div class="dr-property"><p>Points to the topmost element on the paper
</p>
</div><div class="Raphael-section"><h2 id="Raphael" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael(…)<a href="#Raphael" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 59 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L59">&#x27ad;</a></h2>
<div class="extra" id="Raphael-extra"></div></div><div class="dr-method"><p>Creates a canvas object on which to draw.
You must do this first, as all future calls to drawing methods
from this instance will be bound to this canvas.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">container</dt>
<dd class="dr-type"><em class="dr-type-HTMLElement">HTMLElement</em> <em class="dr-type-string">string</em></dd>
<dd class="dr-description">DOM element or its ID which is going to be a parent for drawing surface</dd>
<dt class="dr-param">width</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">height</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param optional">callback</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">callback function which is going to be executed in the context of newly created paper</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">width</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">height</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param optional">callback</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">callback function which is going to be executed in the context of newly created paper</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">all</dt>
<dd class="dr-type"><em class="dr-type-array">array</em></dd>
<dd class="dr-description">(first 3 or 4 elements in the array are equal to [containerID, width, height] or [x, y, width, height]. The rest are element descriptions in format {type: type, &lt;attributes>}). See <a href="#Paper.add" class="dr-link">Paper.add</a>.</dd>
<dt class="dr-param optional">callback</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">callback function which is going to be executed in the context of newly created paper</dd>
</dl>
<p>or
</p>
<dl class="dr-parameters"><dt class="dr-param">onReadyCallback</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">function that is going to be called on DOM ready event. You can also subscribe to this event via Eve’s “DOMLoad” event. In this case method returns <code>undefined</code>.</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Paper" class="dr-link">Paper</a></span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><span class="c">// Each of the following examples create a canvas</span>
<span class="c">// that is 320px wide by 200px high.</span>
<span class="c">// Canvas is created at the viewport’s <span class="d">10</span>,<span class="d">50</span> coordinate.</span>
<b>var</b> paper<span class="s"> = </span>Raphael(<span class="d">10</span>, <span class="d">50</span>, <span class="d">320</span>, <span class="d">200</span>);
<span class="c">// Canvas is created at the top left corner of the #notepad element</span>
<span class="c">// (or its top right corner <b>in</b> dir=<i>"rtl"</i> elements)</span>
<b>var</b> paper<span class="s"> = </span>Raphael(document.getElementById(<i>"notepad"</i>), <span class="d">320</span>, <span class="d">200</span>);
<span class="c">// Same as above</span>
<b>var</b> paper<span class="s"> = </span>Raphael(<i>"notepad"</i>, <span class="d">320</span>, <span class="d">200</span>);
<span class="c">// Image dump</span>
<b>var</b> set<span class="s"> = </span>Raphael([<i>"notepad"</i>, <span class="d">320</span>, <span class="d">200</span>, {
    type: <i>"rect"</i>,
    x: <span class="d">10</span>,
    y: <span class="d">10</span>,
    width: <span class="d">25</span>,
    height: <span class="d">25</span>,
    stroke: <i>"#f00"</i>
}, {
    type: <i>"text"</i>,
    x: <span class="d">30</span>,
    y: <span class="d">40</span>,
    text: <i>"Dump"</i>
}]);
</code></pre>
</div><div class="Raphael-angle-section"><h3 id="Raphael.angle" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.angle(x1, y1, x2, y2, [x3], [y3])<a href="#Raphael.angle" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 402 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L402">&#x27ad;</a></h3>
<div class="extra" id="Raphael.angle-extra"></div></div><div class="dr-method"><p>Returns angle between two or three points
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">x1</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coord of first point</dd>
<dt class="dr-param">y1</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coord of first point</dd>
<dt class="dr-param">x2</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coord of second point</dd>
<dt class="dr-param">y2</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coord of second point</dd>
<dt class="dr-param optional">x3</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x coord of third point</dd>
<dt class="dr-param optional">y3</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y coord of third point</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">angle in degrees.</span></p>
</div><div class="Raphael-animation-section"><h3 id="Raphael.animation" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.animation(params, ms, [easing], [callback])<a href="#Raphael.animation" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3984 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3984">&#x27ad;</a></h3>
<div class="extra" id="Raphael.animation-extra"></div></div><div class="dr-method"><p>Creates an animation object that can be passed to the <a href="#Element.animate" class="dr-link">Element.animate</a> or <a href="#Element.animateWith" class="dr-link">Element.animateWith</a> methods.
See also <a href="#Animation.delay" class="dr-link">Animation.delay</a> and <a href="#Animation.repeat" class="dr-link">Animation.repeat</a> methods.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">params</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">final attributes for the element, see also <a href="#Element.attr" class="dr-link">Element.attr</a></dd>
<dt class="dr-param">ms</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number of milliseconds for animation to run</dd>
<dt class="dr-param optional">easing</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">easing type. Accept one of <a href="#Raphael.easing_formulas" class="dr-link">Raphael.easing_formulas</a> or CSS format: <code>cubic&#x2010;bezier(XX,&#160;XX,&#160;XX,&#160;XX)</code></dd>
<dt class="dr-param optional">callback</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">callback function. Will be called at the end of animation.</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Animation" class="dr-link">Animation</a></span></p>
</div><div class="Raphael-color-section"><h3 id="Raphael.color" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.color(clr)<a href="#Raphael.color" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 601 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L601">&#x27ad;</a></h3>
<div class="extra" id="Raphael.color-extra"></div></div><div class="dr-method"><p>Parses the color string and returns object with all values for the given color.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">clr</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">color string in one of the supported formats (see <a href="#Raphael.getRGB" class="dr-link">Raphael.getRGB</a>)</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Combined RGB <em class="amp">&amp;</em> HSB object in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">r</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">red,</span>
<li><span class="dr-json-key">g</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">green,</span>
<li><span class="dr-json-key">b</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">blue,</span>
<li><span class="dr-json-key">hex</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">color in HTML/CSS format: #••••••,</span>
<li><span class="dr-json-key">error</span><span class="dr-type"><em class="dr-type-boolean">boolean</em></span><span class="dr-json-description"><code>true</code> if string can’t be parsed,</span>
<li><span class="dr-json-key">h</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">hue,</span>
<li><span class="dr-json-key">s</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">saturation,</span>
<li><span class="dr-json-key">v</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">value (brightness),</span>
<li><span class="dr-json-key">l</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">lightness</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-createUUID-section"><h3 id="Raphael.createUUID" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.createUUID()<a href="#Raphael.createUUID" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 475 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L475">&#x27ad;</a></h3>
<div class="extra" id="Raphael.createUUID-extra"></div></div><div class="dr-method"><p>Returns RFC4122, version 4 ID
</p>
</div><div class="Raphael-deg-section"><h3 id="Raphael.deg" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.deg(deg)<a href="#Raphael.deg" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 435 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L435">&#x27ad;</a></h3>
<div class="extra" id="Raphael.deg-extra"></div></div><div class="dr-method"><p>Transform angle to degrees
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">deg</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">angle in radians</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">angle in degrees.</span></p>
</div><div class="Raphael-easing_formulas-section"><h3 id="Raphael.easing_formulas" class="dr-property"><i class="dr-trixie">&#160;</i>Raphael.easing_formulas<a href="#Raphael.easing_formulas" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3408 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3408">&#x27ad;</a></h3>
<div class="extra" id="Raphael.easing_formulas-extra"></div></div><div class="dr-property"><p>Object that contains easing formulas for animation. You could extend it with your own. By default it has following list of easing:
</p>
<ul>
    <li>“linear”</li>
    <li>“&lt;” or “easeIn” or “ease-in”</li>
    <li>“>” or “easeOut” or “ease-out”</li>
    <li>“&lt;>” or “easeInOut” or “ease-in-out”</li>
    <li>“backIn” or “back-in”</li>
    <li>“backOut” or “back-out”</li>
    <li>“elastic”</li>
    <li>“bounce”</li>
</ul>
<p>See also <a href="http://raphaeljs.com/easing.html">Easing demo</a>.</p>
</div><div class="Raphael-el-section"><h3 id="Raphael.el" class="dr-property"><i class="dr-trixie">&#160;</i>Raphael.el<a href="#Raphael.el" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 2344 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L2344">&#x27ad;</a></h3>
<div class="extra" id="Raphael.el-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>You can add your own method to elements. This is usefull when you want to hack default functionality or
want to wrap some common transformation or attributes in one method. In difference to canvas methods,
you can redefine element method at any time. Expending element methods wouldn’t affect set.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>Raphael.el.red<span class="s"> = </span><b>function</b> () {
    <b>this</b>.attr({fill: <i>"#f00"</i>});
};
<span class="c">// then use it</span>
paper.circle(<span class="d">100</span>, <span class="d">100</span>, <span class="d">20</span>).red();
</code></pre>
</div><div class="Raphael-findDotsAtSegment-section"><h3 id="Raphael.findDotsAtSegment" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.findDotsAtSegment(p1x, p1y, c1x, c1y, c2x, c2y, p2x, p2y, t)<a href="#Raphael.findDotsAtSegment" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1154 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L1154">&#x27ad;</a></h3>
<div class="extra" id="Raphael.findDotsAtSegment-extra"></div></div><div class="dr-method"><p>Utility method
Find dot coordinates on the given cubic bezier curve at the given t.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">p1x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x of the first point of the curve</dd>
<dt class="dr-param">p1y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y of the first point of the curve</dd>
<dt class="dr-param">c1x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x of the first anchor of the curve</dd>
<dt class="dr-param">c1y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y of the first anchor of the curve</dd>
<dt class="dr-param">c2x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x of the second anchor of the curve</dd>
<dt class="dr-param">c2y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y of the second anchor of the curve</dd>
<dt class="dr-param">p2x</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">x of the second point of the curve</dd>
<dt class="dr-param">p2y</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">y of the second point of the curve</dd>
<dt class="dr-param">t</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">position on the curve (0..1)</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">point information in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x coordinate of the point</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y coordinate of the point</span>
<li>    m: {<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x coordinate of the left anchor</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y coordinate of the left anchor</span>
</ol></li><li>    }</li><li>    n: {<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x coordinate of the right anchor</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y coordinate of the right anchor</span>
</ol></li><li>    }</li><li>    start: {<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x coordinate of the start of the curve</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y coordinate of the start of the curve</span>
</ol></li><li>    }</li><li>    end: {<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x coordinate of the end of the curve</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y coordinate of the end of the curve</span>
</ol></li><li>    }</li><li><span class="dr-json-key">alpha:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">angle of the curve derivative at the point</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-fn-section"><h3 id="Raphael.fn" class="dr-property"><i class="dr-trixie">&#160;</i>Raphael.fn<a href="#Raphael.fn" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 361 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L361">&#x27ad;</a></h3>
<div class="extra" id="Raphael.fn-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>You can add your own method to the canvas. For example if you want to draw a pie chart,
you can create your own pie chart function and ship it as a Raphaël plugin. To do this
you need to extend the <code>Raphael.fn</code> object. You should modify the <code>fn</code> object before a
Raphaël instance is created, otherwise it will take no effect. Please note that the
ability for namespaced plugins was removed in Raphael 2.0. It is up to the plugin to
ensure any namespacing ensures proper context.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>Raphael.fn.arrow<span class="s"> = </span><b>function</b> (x1, y1, x2, y2, size) {
    <b>return</b> <b>this</b>.path( ... );
};
<span class="c">// or create namespace</span>
Raphael.fn.mystuff<span class="s"> = </span>{
    arrow: <b>function</b> () {…},
    star: <b>function</b> () {…},
    <span class="c">// etc…</span>
};
<b>var</b> paper<span class="s"> = </span>Raphael(<span class="d">10</span>, <span class="d">10</span>, <span class="d">630</span>, <span class="d">480</span>);
<span class="c">// then use it</span>
paper.arrow(<span class="d">10</span>, <span class="d">10</span>, <span class="d">30</span>, <span class="d">30</span>, <span class="d">5</span>).attr({fill: <i>"#f00"</i>});
paper.mystuff.arrow();
paper.mystuff.star();
</code></pre>
</div><div class="Raphael-format-section"><h3 id="Raphael.format" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.format(token, …)<a href="#Raphael.format" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4600 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4600">&#x27ad;</a></h3>
<div class="extra" id="Raphael.format-extra"></div></div><div class="dr-method"><p>Simple format function. Replaces construction of type “<code>{&lt;number>}</code>” to the corresponding argument.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">token</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">string to format</dd>
<dt class="dr-param">…</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">rest of arguments will be treated as parameters for replacement</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">formated string</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><b>var</b> x<span class="s"> = </span><span class="d">10</span>,
    y<span class="s"> = </span><span class="d">20</span>,
    width<span class="s"> = </span><span class="d">40</span>,
    height<span class="s"> = </span><span class="d">50</span>;
<span class="c">// <b>this</b> will draw a rectangular shape equivalent to <i>"M10,20h40v50h-40z"</i></span>
paper.path(Raphael.format(<i>"M{<span class="d">0</span>},{<span class="d">1</span>}h{<span class="d">2</span>}v{<span class="d">3</span>}h{<span class="d">4</span>}z"</i>, x, y, width, height, -width));
</code></pre>
</div><div class="Raphael-fullfill-section"><h3 id="Raphael.fullfill" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.fullfill(token, json)<a href="#Raphael.fullfill" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4630 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4630">&#x27ad;</a></h3>
<div class="extra" id="Raphael.fullfill-extra"></div></div><div class="dr-method"><p>A little bit more advanced format function than <a href="#Raphael.format" class="dr-link">Raphael.format</a>. Replaces construction of type “<code>{&lt;name>}</code>” to the corresponding argument.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">token</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">string to format</dd>
<dt class="dr-param">json</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">object which properties will be used as a replacement</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">formated string</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code><span class="c">// <b>this</b> will draw a rectangular shape equivalent to <i>"M10,20h40v50h-40z"</i></span>
paper.path(Raphael.format(<i>"M{x},{y}h{dim.width}v{dim.height}h{dim['negative width']}z"</i>, {
    x: <span class="d">10</span>,
    y: <span class="d">20</span>,
    dim: {
        width: <span class="d">40</span>,
        height: <span class="d">50</span>,
        <i>"negative width"</i>: -<span class="d">40</span>
    }
}));
</code></pre>
</div><div class="Raphael-getColor-section"><h3 id="Raphael.getColor" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.getColor([value])<a href="#Raphael.getColor" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 977 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L977">&#x27ad;</a></h3>
<div class="extra" id="Raphael.getColor-extra"></div></div><div class="dr-method"><p>On each call returns next colour in the spectrum. To reset it back to red call <a href="#Raphael.getColor.reset" class="dr-link">Raphael.getColor.reset</a>
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param optional">value</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">brightness, default is <code>0.75</code></dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">hex representation of the colour.</span></p>
</div><div class="Raphael-getColor-reset-section"><h4 id="Raphael.getColor.reset" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.getColor.reset()<a href="#Raphael.getColor.reset" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 994 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L994">&#x27ad;</a></h4>
<div class="extra" id="Raphael.getColor.reset-extra"></div></div><div class="dr-method"><p>Resets spectrum position for <a href="#Raphael.getColor" class="dr-link">Raphael.getColor</a> back to red.
</p>
</div><div class="Raphael-getPointAtLength-section"><h3 id="Raphael.getPointAtLength" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.getPointAtLength(path, length)<a href="#Raphael.getPointAtLength" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3317 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3317">&#x27ad;</a></h3>
<div class="extra" id="Raphael.getPointAtLength-extra"></div></div><div class="dr-method"><p>Return coordinates of the point located at the given length on the given path.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">path</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">SVG path string</dd>
<dt class="dr-param">length</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">representation of the point:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">x:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">x coordinate</span>
<li><span class="dr-json-key">y:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">y coordinate</span>
<li><span class="dr-json-key">alpha:</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">angle of derivative</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-getRGB-section"><h3 id="Raphael.getRGB" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.getRGB(colour)<a href="#Raphael.getRGB" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 855 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L855">&#x27ad;</a></h3>
<div class="extra" id="Raphael.getRGB-extra"></div></div><div class="dr-method"><p>Parses colour string as RGB object
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">colour</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">colour string in one of formats:</dd>
</dl>
<ul>
    <li>Colour name (“<code>red</code>”, “<code>green</code>”, “<code>cornflowerblue</code>”, etc)</li>
    <li>#••• — shortened HTML colour: (“<code>#000</code>”, “<code>#fc0</code>”, etc)</li>
    <li>#•••••• — full length HTML colour: (“<code>#000000</code>”, “<code>#bd2300</code>”)</li>
    <li>rgb(•••, •••, •••) — red, green and blue channels’ values: (“<code>rgb(200,&nbsp;100,&nbsp;0)</code>”)</li>
    <li>rgb(•••%, •••%, •••%) — same as above, but in %: (“<code>rgb(100%,&nbsp;175%,&nbsp;0%)</code>”)</li>
    <li>hsb(•••, •••, •••) — hue, saturation and brightness values: (“<code>hsb(0.5,&nbsp;0.25,&nbsp;1)</code>”)</li>
    <li>hsb(•••%, •••%, •••%) — same as above, but in %</li>
    <li>hsl(•••, •••, •••) — same as hsb</li>
    <li>hsl(•••%, •••%, •••%) — same as hsb</li>
</ul>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">RGB object in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">r</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">red,</span>
<li><span class="dr-json-key">g</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">green,</span>
<li><span class="dr-json-key">b</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">blue</span>
<li><span class="dr-json-key">hex</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">color in HTML/CSS format: #••••••,</span>
<li><span class="dr-json-key">error</span><span class="dr-type"><em class="dr-type-boolean">boolean</em></span><span class="dr-json-description">true if string can’t be parsed</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-getSubpath-section"><h3 id="Raphael.getSubpath" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.getSubpath(path, from, to)<a href="#Raphael.getSubpath" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3332 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3332">&#x27ad;</a></h3>
<div class="extra" id="Raphael.getSubpath-extra"></div></div><div class="dr-method"><p>Return subpath of a given path from given length to given length.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">path</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">SVG path string</dd>
<dt class="dr-param">from</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">position of the start of the segment</dd>
<dt class="dr-param">to</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">position of the end of the segment</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">pathstring for the segment</span></p>
</div><div class="Raphael-getTotalLength-section"><h3 id="Raphael.getTotalLength" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.getTotalLength(path)<a href="#Raphael.getTotalLength" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 3298 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L3298">&#x27ad;</a></h3>
<div class="extra" id="Raphael.getTotalLength-extra"></div></div><div class="dr-method"><p>Returns length of the given path in pixels.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">path</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">SVG path string.</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">length.</span></p>
</div><div class="Raphael-hsb-section"><h3 id="Raphael.hsb" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.hsb(h, s, b)<a href="#Raphael.hsb" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 937 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L937">&#x27ad;</a></h3>
<div class="extra" id="Raphael.hsb-extra"></div></div><div class="dr-method"><p>Converts HSB values to hex representation of the colour.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">h</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">hue</dd>
<dt class="dr-param">s</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">saturation</dd>
<dt class="dr-param">b</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">value or brightness</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">hex representation of the colour.</span></p>
</div><div class="Raphael-hsb2rgb-section"><h3 id="Raphael.hsb2rgb" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.hsb2rgb(h, s, v)<a href="#Raphael.hsb2rgb" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 651 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L651">&#x27ad;</a></h3>
<div class="extra" id="Raphael.hsb2rgb-extra"></div></div><div class="dr-method"><p>Converts HSB values to RGB object.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">h</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">hue</dd>
<dt class="dr-param">s</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">saturation</dd>
<dt class="dr-param">v</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">value or brightness</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">RGB object in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">r</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">red,</span>
<li><span class="dr-json-key">g</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">green,</span>
<li><span class="dr-json-key">b</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">blue,</span>
<li><span class="dr-json-key">hex</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">color in HTML/CSS format: #••••••</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-hsl-section"><h3 id="Raphael.hsl" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.hsl(h, s, l)<a href="#Raphael.hsl" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 951 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L951">&#x27ad;</a></h3>
<div class="extra" id="Raphael.hsl-extra"></div></div><div class="dr-method"><p>Converts HSL values to hex representation of the colour.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">h</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">hue</dd>
<dt class="dr-param">s</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">saturation</dd>
<dt class="dr-param">l</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">luminosity</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">hex representation of the colour.</span></p>
</div><div class="Raphael-hsl2rgb-section"><h3 id="Raphael.hsl2rgb" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.hsl2rgb(h, s, l)<a href="#Raphael.hsl2rgb" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 688 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L688">&#x27ad;</a></h3>
<div class="extra" id="Raphael.hsl2rgb-extra"></div></div><div class="dr-method"><p>Converts HSL values to RGB object.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">h</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">hue</dd>
<dt class="dr-param">s</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">saturation</dd>
<dt class="dr-param">l</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">luminosity</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">RGB object in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">r</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">red,</span>
<li><span class="dr-json-key">g</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">green,</span>
<li><span class="dr-json-key">b</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">blue,</span>
<li><span class="dr-json-key">hex</span><span class="dr-type"><em class="dr-type-string">string</em></span><span class="dr-json-description">color in HTML/CSS format: #••••••</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-is-section"><h3 id="Raphael.is" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.is(o, type)<a href="#Raphael.is" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 374 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L374">&#x27ad;</a></h3>
<div class="extra" id="Raphael.is-extra"></div></div><div class="dr-method"><p>Handfull replacement for <code>typeof</code> operator.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">o</dt>
<dd class="dr-type"><em class="dr-type-…">…</em></dd>
<dd class="dr-description">any object or primitive</dd>
<dt class="dr-param">type</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">name of the type, i.e. “string”, “function”, “number”, etc.</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-boolean">boolean</em> <span class="dr-description">is given value is of given type</span></p>
</div><div class="Raphael-matrix-section"><h3 id="Raphael.matrix" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.matrix(a, b, c, d, e, f)<a href="#Raphael.matrix" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1943 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L1943">&#x27ad;</a></h3>
<div class="extra" id="Raphael.matrix-extra"></div></div><div class="dr-method"><p>Utility method
Returns matrix based on given parameters.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">a</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">b</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">c</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">d</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">e</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
<dt class="dr-param">f</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">&#160;</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description"><a href="#Matrix" class="dr-link">Matrix</a></span></p>
</div><div class="Raphael-ninja-section"><h3 id="Raphael.ninja" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.ninja()<a href="#Raphael.ninja" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4667 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4667">&#x27ad;</a></h3>
<div class="extra" id="Raphael.ninja-extra"></div></div><div class="dr-method"><p>If you want to leave no trace of Raphaël (Well, Raphaël creates only one global variable <code>Raphael</code>, but anyway.) You can use <code>ninja</code> method.
Beware, that in this case plugins could stop working, because they are depending on global variable existance.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Raphael object</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>(<b>function</b> (local_raphael) {
    <b>var</b> paper<span class="s"> = </span>local_raphael(<span class="d">10</span>, <span class="d">10</span>, <span class="d">320</span>, <span class="d">200</span>);
    …
})(Raphael.ninja());
</code></pre>
</div><div class="Raphael-parsePathString-section"><h3 id="Raphael.parsePathString" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.parsePathString(pathString)<a href="#Raphael.parsePathString" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1047 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L1047">&#x27ad;</a></h3>
<div class="extra" id="Raphael.parsePathString-extra"></div></div><div class="dr-method"><p>Utility method
Parses given path string into an array of arrays of path segments.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">pathString</dt>
<dd class="dr-type"><em class="dr-type-string">string</em> <em class="dr-type-array">array</em></dd>
<dd class="dr-description">path string or array of segments (in the last case it will be returned straight away)</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-array">array</em> <span class="dr-description">array of segments.</span></p>
</div><div class="Raphael-parseTransformString-section"><h3 id="Raphael.parseTransformString" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.parseTransformString(TString)<a href="#Raphael.parseTransformString" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1092 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L1092">&#x27ad;</a></h3>
<div class="extra" id="Raphael.parseTransformString-extra"></div></div><div class="dr-method"><p>Utility method
Parses given path string into an array of transformations.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">TString</dt>
<dd class="dr-type"><em class="dr-type-string">string</em> <em class="dr-type-array">array</em></dd>
<dd class="dr-description">transform string or array of transformations (in the last case it will be returned straight away)</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-array">array</em> <span class="dr-description">array of transformations.</span></p>
</div><div class="Raphael-path2curve-section"><h3 id="Raphael.path2curve" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.path2curve(pathString)<a href="#Raphael.path2curve" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1926 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L1926">&#x27ad;</a></h3>
<div class="extra" id="Raphael.path2curve-extra"></div></div><div class="dr-method"><p>Utility method
Converts path to a new path where all segments are cubic bezier curves.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">pathString</dt>
<dd class="dr-type"><em class="dr-type-string">string</em> <em class="dr-type-array">array</em></dd>
<dd class="dr-description">path string or array of segments</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-array">array</em> <span class="dr-description">array of segments.</span></p>
</div><div class="Raphael-pathToRelative-section"><h3 id="Raphael.pathToRelative" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.pathToRelative(pathString)<a href="#Raphael.pathToRelative" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 1913 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L1913">&#x27ad;</a></h3>
<div class="extra" id="Raphael.pathToRelative-extra"></div></div><div class="dr-method"><p>Utility method
Converts path to relative form
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">pathString</dt>
<dd class="dr-type"><em class="dr-type-string">string</em> <em class="dr-type-array">array</em></dd>
<dd class="dr-description">path string or array of segments</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-array">array</em> <span class="dr-description">array of segments.</span></p>
</div><div class="Raphael-rad-section"><h3 id="Raphael.rad" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.rad(deg)<a href="#Raphael.rad" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 423 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L423">&#x27ad;</a></h3>
<div class="extra" id="Raphael.rad-extra"></div></div><div class="dr-method"><p>Transform angle to radians
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">deg</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">angle in degrees</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">angle in radians.</span></p>
</div><div class="Raphael-registerFont-section"><h3 id="Raphael.registerFont" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.registerFont(font)<a href="#Raphael.registerFont" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4405 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4405">&#x27ad;</a></h3>
<div class="extra" id="Raphael.registerFont-extra"></div></div><div class="dr-method"><p>Adds given font to the registered set of fonts for Raphaël. Should be used as an internal call from within Cufón’s font file.
Returns original parameter, so it could be used with chaining.
</p>
<a href="http://wiki.github.com/sorccu/cufon/about">More about Cufón and how to convert your font form TTF, OTF, etc to JavaScript file.</a>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">font</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">the font to register</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">the font you passed in</span></p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>Cufon.registerFont(Raphael.registerFont({…}));
</code></pre>
</div><div class="Raphael-rgb-section"><h3 id="Raphael.rgb" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.rgb(r, g, b)<a href="#Raphael.rgb" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 965 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L965">&#x27ad;</a></h3>
<div class="extra" id="Raphael.rgb-extra"></div></div><div class="dr-method"><p>Converts RGB values to hex representation of the colour.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">r</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">red</dd>
<dt class="dr-param">g</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">green</dd>
<dt class="dr-param">b</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">blue</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">hex representation of the colour.</span></p>
</div><div class="Raphael-rgb2hsb-section"><h3 id="Raphael.rgb2hsb" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.rgb2hsb(r, g, b)<a href="#Raphael.rgb2hsb" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 728 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L728">&#x27ad;</a></h3>
<div class="extra" id="Raphael.rgb2hsb-extra"></div></div><div class="dr-method"><p>Converts RGB values to HSB object.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">r</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">red</dd>
<dt class="dr-param">g</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">green</dd>
<dt class="dr-param">b</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">blue</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">HSB object in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">h</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">hue</span>
<li><span class="dr-json-key">s</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">saturation</span>
<li><span class="dr-json-key">b</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">brightness</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-rgb2hsl-section"><h3 id="Raphael.rgb2hsl" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.rgb2hsl(r, g, b)<a href="#Raphael.rgb2hsl" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 762 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L762">&#x27ad;</a></h3>
<div class="extra" id="Raphael.rgb2hsl-extra"></div></div><div class="dr-method"><p>Converts RGB values to HSL object.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">r</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">red</dd>
<dt class="dr-param">g</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">green</dd>
<dt class="dr-param">b</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">blue</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">HSL object in format:</span></p>
<ol class="dr-json"><li>{<ol class="dr-json"><li><span class="dr-json-key">h</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">hue</span>
<li><span class="dr-json-key">s</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">saturation</span>
<li><span class="dr-json-key">l</span><span class="dr-type"><em class="dr-type-number">number</em></span><span class="dr-json-description">luminosity</span>
</ol></li><li>}</li></ol>
</div><div class="Raphael-setWindow-section"><h3 id="Raphael.setWindow" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.setWindow(newwin)<a href="#Raphael.setWindow" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 493 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L493">&#x27ad;</a></h3>
<div class="extra" id="Raphael.setWindow-extra"></div></div><div class="dr-method"><p>Used when you need to draw in <code>&lt;iframe></code>. Switched window to the iframe one.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">newwin</dt>
<dd class="dr-type"><em class="dr-type-window">window</em></dd>
<dd class="dr-description">new window object</dd>
</dl>
</div><div class="Raphael-snapTo-section"><h3 id="Raphael.snapTo" class="dr-method"><i class="dr-trixie">&#160;</i>Raphael.snapTo(values, value, [tolerance])<a href="#Raphael.snapTo" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 449 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L449">&#x27ad;</a></h3>
<div class="extra" id="Raphael.snapTo-extra"></div></div><div class="dr-method"><p>Snaps given value to given grid.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">values</dt>
<dd class="dr-type"><em class="dr-type-array">array</em> <em class="dr-type-number">number</em></dd>
<dd class="dr-description">given array of values or step of the grid</dd>
<dt class="dr-param">value</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">value to adjust</dd>
<dt class="dr-param optional">tolerance</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">tolerance for snapping. Default is <code>10</code>.</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-number">number</em> <span class="dr-description">adjusted value.</span></p>
</div><div class="Raphael-st-section"><h3 id="Raphael.st" class="dr-property"><i class="dr-trixie">&#160;</i>Raphael.st<a href="#Raphael.st" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4691 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4691">&#x27ad;</a></h3>
<div class="extra" id="Raphael.st-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-object">object</em><p>You can add your own method to elements and sets. It is wise to add a set method for each element method
you added, so you will be able to call the same method on sets too.
See also <a href="#Raphael.el" class="dr-link">Raphael.el</a>.
</p>
<p class="header">Usage
</p>
<pre class="javascript code"><code>Raphael.el.red<span class="s"> = </span><b>function</b> () {
    <b>this</b>.attr({fill: <i>"#f00"</i>});
};
Raphael.st.red<span class="s"> = </span><b>function</b> () {
    <b>this</b>.forEach(<b>function</b> (el) {
        el.red();
    });
};
<span class="c">// then use it</span>
paper.set(paper.circle(<span class="d">100</span>, <span class="d">100</span>, <span class="d">20</span>), paper.circle(<span class="d">110</span>, <span class="d">100</span>, <span class="d">20</span>)).red();
</code></pre>
</div><div class="Raphael-svg-section"><h3 id="Raphael.svg" class="dr-property"><i class="dr-trixie">&#160;</i>Raphael.svg<a href="#Raphael.svg" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 327 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L327">&#x27ad;</a></h3>
<div class="extra" id="Raphael.svg-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-boolean">boolean</em><p><code>true</code> if browser supports SVG.
</p>
</div><div class="Raphael-type-section"><h3 id="Raphael.type" class="dr-property"><i class="dr-trixie">&#160;</i>Raphael.type<a href="#Raphael.type" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 309 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L309">&#x27ad;</a></h3>
<div class="extra" id="Raphael.type-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-string">string</em><p>Can be “SVG”, “VML” or empty, depending on browser support.
</p>
</div><div class="Raphael-vml-section"><h3 id="Raphael.vml" class="dr-property"><i class="dr-trixie">&#160;</i>Raphael.vml<a href="#Raphael.vml" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 333 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L333">&#x27ad;</a></h3>
<div class="extra" id="Raphael.vml-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-boolean">boolean</em><p><code>true</code> if browser supports VML.
</p>
</div><div class="Set-section"><h2 id="Set" class="undefined"><i class="dr-trixie">&#160;</i>Set<a href="#Set" title="Link to this section" class="dr-hash">&#x2693;</a></h2>
<div class="extra" id="Set-extra"></div></div><div class="Set-clear-section"><h3 id="Set.clear" class="dr-method"><i class="dr-trixie">&#160;</i>Set.clear()<a href="#Set.clear" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4269 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4269">&#x27ad;</a></h3>
<div class="extra" id="Set.clear-extra"></div></div><div class="dr-method"><p>Removeds all elements from the set
</p>
</div><div class="Set-exclude-section"><h3 id="Set.exclude" class="dr-method"><i class="dr-trixie">&#160;</i>Set.exclude(element)<a href="#Set.exclude" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4324 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4324">&#x27ad;</a></h3>
<div class="extra" id="Set.exclude-extra"></div></div><div class="dr-method"><p>Removes given element from the set
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">element</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">element to remove</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-boolean">boolean</em> <span class="dr-description"><code>true</code> if object was found <em class="amp">&amp;</em> removed from the set</span></p>
</div><div class="Set-forEach-section"><h3 id="Set.forEach" class="dr-method"><i class="dr-trixie">&#160;</i>Set.forEach(callback, thisArg)<a href="#Set.forEach" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4233 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4233">&#x27ad;</a></h3>
<div class="extra" id="Set.forEach-extra"></div></div><div class="dr-method"><p>Executes given function for each element in the set.
</p>
<p>If function returns <code>false</code> it will stop loop running.
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">callback</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">function to run</dd>
<dt class="dr-param">thisArg</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context object for the callback</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">Set object</span></p>
</div><div class="Set-pop-section"><h3 id="Set.pop" class="dr-method"><i class="dr-trixie">&#160;</i>Set.pop()<a href="#Set.pop" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4215 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4215">&#x27ad;</a></h3>
<div class="extra" id="Set.pop-extra"></div></div><div class="dr-method"><p>Removes last element and returns it.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">element</span></p>
</div><div class="Set-push-section"><h3 id="Set.push" class="dr-method"><i class="dr-trixie">&#160;</i>Set.push()<a href="#Set.push" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4195 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4195">&#x27ad;</a></h3>
<div class="extra" id="Set.push-extra"></div></div><div class="dr-method"><p>Adds each argument to the current set.
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">original element</span></p>
</div><div class="Set-splice-section"><h3 id="Set.splice" class="dr-method"><i class="dr-trixie">&#160;</i>Set.splice(index, count, [insertion…])<a href="#Set.splice" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 4287 in the source" href="https://github.com/DmitryBaranovskiy/raphael/blob/master/raphael.core.js#L4287">&#x27ad;</a></h3>
<div class="extra" id="Set.splice-extra"></div></div><div class="dr-method"><p>Removes given element from the set
</p>
<p class="header">Parameters
</p>
<dl class="dr-parameters"><dt class="dr-param">index</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">position of the deletion</dd>
<dt class="dr-param">count</dt>
<dd class="dr-type"><em class="dr-type-number">number</em></dd>
<dd class="dr-description">number of element to remove</dd>
<dt class="dr-param optional">insertion…</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">elements to insert</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">set elements that were deleted</span></p>
</div><div class="eve-section"><h2 id="eve" class="dr-method"><i class="dr-trixie">&#160;</i>eve(name, scope, varargs)<a href="#eve" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 34 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L34">&#x27ad;</a></h2>
<div class="extra" id="eve-extra"></div></div><div class="dr-method"><p>Fires event with given <code>name</code>, given scope and other parameters.
</p>
<p class="header">Arguments
</p>
<dl class="dr-parameters"><dt class="dr-param">name</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">name of the event, dot (<code>.</code>) or slash (<code>/</code>) separated</dd>
<dt class="dr-param">scope</dt>
<dd class="dr-type"><em class="dr-type-object">object</em></dd>
<dd class="dr-description">context for the event handlers</dd>
<dt class="dr-param">varargs</dt>
<dd class="dr-type"><em class="dr-type-...">...</em></dd>
<dd class="dr-description">the rest of arguments will be sent to event handlers</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-object">object</em> <span class="dr-description">array of returned values from the listeners</span></p>
</div><div class="eve-listeners-section"><h3 id="eve.listeners" class="dr-method"><i class="dr-trixie">&#160;</i>eve.listeners(name)<a href="#eve.listeners" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 106 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L106">&#x27ad;</a></h3>
<div class="extra" id="eve.listeners-extra"></div></div><div class="dr-method"><p>Internal method which gives you array of all event handlers that will be triggered by the given <code>name</code>.
</p>
<p class="header">Arguments
</p>
<dl class="dr-parameters"><dt class="dr-param">name</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">name of the event, dot (<code>.</code>) or slash (<code>/</code>) separated</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-array">array</em> <span class="dr-description">array of event handlers</span></p>
</div><div class="eve-nt-section"><h3 id="eve.nt" class="dr-method"><i class="dr-trixie">&#160;</i>eve.nt([subname])<a href="#eve.nt" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 203 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L203">&#x27ad;</a></h3>
<div class="extra" id="eve.nt-extra"></div></div><div class="dr-method"><p>Could be used inside event handler to figure out actual name of the event.
</p>
<p class="header">Arguments
</p>
<dl class="dr-parameters"><dt class="dr-param optional">subname</dt>
<dd class="dr-optional">optional</dd>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">subname of the event</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-string">string</em> <span class="dr-description">name of the event, if <code>subname</code> is not specified</span></p>
<p>or
</p>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-boolean">boolean</em> <span class="dr-description"><code>true</code>, if current event’s name contains <code>subname</code></span></p>
</div><div class="eve-off-section"><h3 id="eve.off" class="dr-method"><i class="dr-trixie">&#160;</i>eve.off(name, f)<a href="#eve.off" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 220 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L220">&#x27ad;</a></h3>
<div class="extra" id="eve.off-extra"></div></div><div class="dr-method"><p>Removes given function from the list of event listeners assigned to given name.
</p>
<p class="header">Arguments
</p>
<dl class="dr-parameters"><dt class="dr-param">name</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">name of the event, dot (<code>.</code>) or slash (<code>/</code>) separated, with optional wildcards</dd>
<dt class="dr-param">f</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">event handler function</dd>
</dl>
</div><div class="eve-on-section"><h3 id="eve.on" class="dr-method"><i class="dr-trixie">&#160;</i>eve.on(name, f)<a href="#eve.on" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 161 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L161">&#x27ad;</a></h3>
<div class="extra" id="eve.on-extra"></div></div><div class="dr-method"><p>Binds given event handler with a given name. You can use wildcards “<code>*</code>” for the names:
</p>
<pre class="javascript code"><code>eve.on(<i>"*.under.*"</i>, f);
eve(<i>"mouse.under.floor"</i>); <span class="c">// triggers f</span>
</code></pre>
<p>Use <a href="#eve" class="dr-link">eve</a> to trigger the listener.
</p>
<p class="header">Arguments
</p>
<dl class="dr-parameters"><dt class="dr-param">name</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">name of the event, dot (<code>.</code>) or slash (<code>/</code>) separated, with optional wildcards</dd>
<dt class="dr-param">f</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">event handler function</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-function">function</em> <span class="dr-description">returned function accepts a single numeric parameter that represents z-index of the handler. It is an optional feature and only used when you need to ensure that some subset of handlers will be invoked in a given order, despite of the order of assignment. </span></p>
<p class="header">Example:
</p>
<pre class="javascript code"><code>eve.on(<i>"mouse"</i>, eat)(<span class="d">2</span>);
eve.on(<i>"mouse"</i>, scream);
eve.on(<i>"mouse"</i>, <b>catch</b>)(<span class="d">1</span>);
</code></pre>
<p>This will ensure that <code>catch</code> function will be called before <code>eat</code>.
If you want to put your handler before non-indexed handlers, specify a negative value.
Note: I assume most of the time you don’t need to worry about z-index, but it’s nice to have this feature “just in case”.
</p>
</div><div class="eve-once-section"><h3 id="eve.once" class="dr-method"><i class="dr-trixie">&#160;</i>eve.once(name, f)<a href="#eve.once" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 295 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L295">&#x27ad;</a></h3>
<div class="extra" id="eve.once-extra"></div></div><div class="dr-method"><p>Binds given event handler with a given name to only run once then unbind itself.
</p>
<pre class="javascript code"><code>eve.once(<i>"login"</i>, f);
eve(<i>"login"</i>); <span class="c">// triggers f</span>
eve(<i>"login"</i>); <span class="c">// no listeners</span>
</code></pre>
<p>Use <a href="#eve" class="dr-link">eve</a> to trigger the listener.
</p>
<p class="header">Arguments
</p>
<dl class="dr-parameters"><dt class="dr-param">name</dt>
<dd class="dr-type"><em class="dr-type-string">string</em></dd>
<dd class="dr-description">name of the event, dot (<code>.</code>) or slash (<code>/</code>) separated, with optional wildcards</dd>
<dt class="dr-param">f</dt>
<dd class="dr-type"><em class="dr-type-function">function</em></dd>
<dd class="dr-description">event handler function</dd>
</dl>
<p class="dr-returns"><strong class="dr-title">Returns:</strong> <em class="dr-type-function">function</em> <span class="dr-description">same return function as <a href="#eve.on" class="dr-link">eve.on</a></span></p>
</div><div class="eve-stop-section"><h3 id="eve.stop" class="dr-method"><i class="dr-trixie">&#160;</i>eve.stop()<a href="#eve.stop" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 186 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L186">&#x27ad;</a></h3>
<div class="extra" id="eve.stop-extra"></div></div><div class="dr-method"><p>Is used inside an event handler to stop the event, preventing any subsequent listeners from firing.
</p>
</div><div class="eve-unbind-section"><h3 id="eve.unbind" class="dr-method"><i class="dr-trixie">&#160;</i>eve.unbind()<a href="#eve.unbind" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 226 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L226">&#x27ad;</a></h3>
<div class="extra" id="eve.unbind-extra"></div></div><div class="dr-method"><p>See <a href="#eve.off" class="dr-link">eve.off</a>
</p>
</div><div class="eve-version-section"><h3 id="eve.version" class="dr-property"><i class="dr-trixie">&#160;</i>eve.version<a href="#eve.version" title="Link to this section" class="dr-hash">&#x2693;</a><a class="dr-sourceline" title="Go to line 309 in the source" href="https://github.com/DmitryBaranovskiy/eve/blob/master/eve.js#L309">&#x27ad;</a></h3>
<div class="extra" id="eve.version-extra"></div></div><div class="dr-property"><em class="dr-type dr-type-string">string</em><p>Current version of the library.
</p>
</div></div></div>
<script src="raphael.js"></script>
<script src="reference.js"></script>
</body></html>