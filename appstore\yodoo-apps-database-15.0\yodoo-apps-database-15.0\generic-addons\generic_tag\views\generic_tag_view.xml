<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <menuitem id="menu_generic_tags_root"
                groups="generic_tag.group_tags_user"
                sequence="16"
                name="Tags"
                web_icon="generic_tag,static/description/icon.png"/>

    <menuitem id="menu_generic_tags_all"
                parent="generic_tag.menu_generic_tags_root"
                sequence="10"
                name="All Tags"/>

    <menuitem id="menu_generic_tags_configuration_root"
                parent="generic_tag.menu_generic_tags_root"
                sequence="220"
                name="Configuration"
                groups="group_tags_manager"/>

    <record id="view_generic_tag_search" model="ir.ui.view">
        <field name="name">generic.tag.search</field>
        <field name="model">generic.tag</field>
        <field name="arch" type="xml">
            <search>
                <field name="model_id"/>
                <field name="category_id"/>
                <field name="name"/>
                <field name="code"/>

                <group expand="0" string="Group By...">
                    <filter string="Model" name="group_by_model" domain="[]" context="{'group_by':'model_id'}"/>
                    <filter string="Category" name="group_by_category" domain="[]" context="{'group_by':'category_id'}"/>
                </group>

            </search>
        </field>
    </record>

    <record id="view_generic_tag_tree" model="ir.ui.view">
        <field name="name">generic.tag.tree</field>
        <field name="model">generic.tag</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="model_id" widget="selection" invisible="'hide_model' in context"/>
                <field name="category_id" domain="[('model_id','=',model_id)]"
                        context="{'default_model_id': model_id}" attrs="{'readonly': [('model_id','=',False)]}"/>
                <field name="name"/>
                <field name="code"/>
                <field name="comment"/>
                <field name="active"/>
                <field name="objects_count"/>
                <button name="action_show_objects" type="object" string="Show Objects" icon="fa-search"/>
            </tree>
        </field>
    </record>

    <record id="view_generic_tag_form" model="ir.ui.view">
        <field name="name">generic.tag.form</field>
        <field name="model">generic.tag</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_show_objects"
                                type="object" icon="fa-bars"
                                class="oe_stat_button">
                            <field name="objects_count" string="Objects" widget="statinfo"/>
                        </button>
                    </div>
                    <field name="active" invisible='True'/>
                    <widget name="web_ribbon"
                            title="Archived"
                            bg_color="bg-danger"
                            attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <h1 class="o_row">
                            <field name="complete_name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="model_id" widget="selection" invisible="'hide_model' in context"/>
                            <field name="category_id" domain="[('model_id','=',model_id)]"
                                   context="{'default_model_id': model_id}" attrs="{'readonly': [('model_id','=',False)]}"/>
                            <field name="group_ids" widget="many2many_tags"
                                   groups="generic_tag.group_tags_manager"/>
                        </group>
                    </group>
                    <field name="comment" placeholder="Comment..."/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_generic_tags_view" model="ir.actions.act_window">
        <field name="name">Tags</field>
        <field name="res_model">generic.tag</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_generic_tag_tree"/>
    </record>

    <menuitem action="action_generic_tags_view" id="menu_action_generic_tags_view"
                parent="menu_generic_tags_all" sequence="10"/>
</odoo>
