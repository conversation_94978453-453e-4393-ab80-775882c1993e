# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_tag_automation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 15:06+0000\n"
"PO-Revision-Date: 2023-07-26 09:29+0000\n"
"Last-Translator: Den_<PERSON>haraf <<EMAIL>>\n"
"Language-Team: Ukrainian <http://weblate.crnd.pro/projects/"
"generic-addons-15-0/generic_tag_automation/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: generic_tag_automation
#: model:ir.model.fields,field_description:generic_tag_automation.field_base_automation__state
#: model:ir.model.fields,field_description:generic_tag_automation.field_ir_actions_server__state
#: model:ir.model.fields,field_description:generic_tag_automation.field_ir_cron__state
msgid "Action To Do"
msgstr "Дія, яку необхідно зробити"

#. module: generic_tag_automation
#: model:ir.model.fields,field_description:generic_tag_automation.field_base_automation__act_add_tag_ids
msgid "Add Tags"
msgstr "Додати Тег"

#. module: generic_tag_automation
#: model:ir.model,name:generic_tag_automation.model_base_automation
msgid "Automated Action"
msgstr "Автоматизована дія"

#. module: generic_tag_automation
#: model:ir.model.fields,field_description:generic_tag_automation.field_base_automation__act_remove_tag_ids
msgid "Remove Tags"
msgstr "Видалити теги"

#. module: generic_tag_automation
#: model:ir.model,name:generic_tag_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Дії на сервері"

#. module: generic_tag_automation
#: model:ir.model.fields.selection,name:generic_tag_automation.selection__ir_actions_server__state__set_tag
msgid "Set Tag"
msgstr "Встановити Тег"

#. module: generic_tag_automation
#: model:ir.model.fields,help:generic_tag_automation.field_base_automation__act_add_tag_ids
msgid "Specify tags to be added to object this rule is applied to"
msgstr ""
"Укажіть теги, які потрібно додати до об’єкта, до якого застосовується це "
"правило"

#. module: generic_tag_automation
#: model:ir.model.fields,help:generic_tag_automation.field_base_automation__act_remove_tag_ids
msgid "Specify tags to be removed from object this rule is applied to"
msgstr ""
"Укажіть теги, які потрібно видалити з об’єкта, до якого застосовується це "
"правило"

#. module: generic_tag_automation
#: model:ir.model.fields,help:generic_tag_automation.field_base_automation__state
#: model:ir.model.fields,help:generic_tag_automation.field_ir_actions_server__state
#: model:ir.model.fields,help:generic_tag_automation.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other "
"server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""

#~ msgid "Add Followers"
#~ msgstr "Додати підписників"

#~ msgid "Create a new Record"
#~ msgstr "Створіть новий запис"

#~ msgid "Execute Python Code"
#~ msgstr "Виконати код Python"

#~ msgid "Execute several actions"
#~ msgstr "Виконати декілька дій"

#~ msgid "Send Email"
#~ msgstr "Надіслати ел. листа"

#~ msgid ""
#~ "Type of server action. The following values are available:\n"
#~ "- 'Execute Python Code': a block of python code that will be executed\n"
#~ "- 'Create or Copy a new Record': create a new record with new values, or "
#~ "copy an existing record in your database\n"
#~ "- 'Write on a Record': update the values of a record\n"
#~ "- 'Execute several actions': define an action that triggers several other "
#~ "server actions\n"
#~ "- 'Add Followers': add followers to a record (available in Discuss)\n"
#~ "- 'Send Email': automatically send an email (available in email_template)"
#~ msgstr ""
#~ "Тип дії сервера. Доступні наступні значення:\n"
#~ "- 'Execute Python Code': блок коду python, який буде виконуватися\n"
#~ "- \"Створити або скопіювати новий запис\": створити новий запис із новими "
#~ "значеннями або скопіювати існуючий запис у вашу базу даних\n"
#~ "- \"Написати на запис\": оновіть значення запису\n"
#~ "- \"Виконати кілька дій\": визначити дію, яка запускає кілька інших дій "
#~ "сервера\n"
#~ "- \"Додати послідовників\": додавати послідовників до запису (доступно в "
#~ "Обговоренні)\n"
#~ "- \"Надіслати електронний лист\": автоматично надсилатиме електронний "
#~ "лист (доступний в email_template)"

#~ msgid "Update the Record"
#~ msgstr "Оновити запис"
