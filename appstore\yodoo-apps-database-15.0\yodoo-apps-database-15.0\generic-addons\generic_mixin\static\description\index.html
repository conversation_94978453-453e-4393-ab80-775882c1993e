<section style="max-width: 896px; margin: 0 auto;">
<div align="center">
<h1 style="color:#875A7B;">Generic Mixins</h1>
</div>
<div align="center">
<p>
    <a href="https://github.com/crnd-inc/generic-addons">
        <img alt="pipeline-pass" src="https://img.shields.io/badge/pipeline-pass-brightgreen.png" /></a>
    <a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html">
        <img alt="License: LGPL-3" src="https://img.shields.io/badge/license-LGPL--3-blue.png" /></a>
    <a href="https://crnd.pro/">
        <img alt="CR&amp;D" src="https://img.shields.io/badge/maintainer-CR&amp;D-purple.png" /></a>
    <a href="https://github.com/crnd-inc/generic-addons/tree/12.0/generic_mixin">
        <img alt="GitHub" src="https://img.shields.io/badge/GitHub-Generic_Mixin-green.png" /></a></p>
<p><br></p>
</div>
</section>

<section style="margin: 0 auto; max-width: 896px;">
<div>
<p>This is technical addon, that contains model mixins, that may be useful in develompent of other addons.</p>
<p>Following mixins ara available:</p>
<ul>
<li><i>generic.mixin.delegation.interface</i> - Mixin for Interface concept model.</li>
<li><i>generic.mixin.delegation.implementation</i> - Mixin for Implementation of Interface concept model.</li>
<li><i>generic.mixin.name_with_code</i> - just add <i>name</i> and <i>code</i> fields to model, on name changed - compute code automatically.</li>
<li><i>generic.mixin.uniq_name_code</i> - add *unique* constraint to name and code fields.</li>
<li><i>generic.mixin.name.by.sequence</i> - Add *name* field to your model, that will be automatically filled with value based on <i>ir.sequence</i> (in same way as in sale order, or other odoo models)</li>
<li><i>generic.mixin.transaction.utils</i> - utility methods to handle transactions in Odoo.</li>
<li><i>generic.mixin.no.unlink</i> - deny unlink of specific records in model.</li>
<li><i>generic.parent.names</i> - implement <i>name_get</i> and <i>name_search</i> for hierarchial models in generic way</li>
<li><i>generic.mixin.refresh.view</i> - provides methods to refresh views in browser from python</li>
<li><i>generic.mixin.track.changes</i> - provides convenient mechanism to handle changes of specific fields.</li>
<li><i>generic.mixin.uuid</i> - Easy way to generate UUIDs for records in your models.</li>
</ul>

<p>Also, there are <i>pre_write</i> and <i>post_write</i> decoratrors, that could be used
together with <i>generic.mixin.track.changes</i>.</p>

</div>
</section>

<section style="max-width: 896px; margin: 0 auto;">
<p>This module is part of the Bureaucrat ITSM project. You can try it by the references below.</p>
</section>

<section style="max-width: 896px; margin: 0 auto;">
<div>
<h3>Bug Tracker</h3>
<p>Bugs are tracked on <a href="https://github.com/crnd-inc/generic-addons/issues">GitHub Issues</a>. In case of trouble, please check there if your issue has already been reported.
</p>
<p><br></p>
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color:#875A7B;">Launch your own ITSM system in 60 seconds:</h2>
<p>Create your own <a href="https://yodoo.systems/saas/template/">Bureaucrat ITSM</a> database
<a href="https://yodoo.systems"><img alt="yodoo.systems" src="https://img.shields.io/badge/powered%20by-yodoo.systems-00a09d.png"/></a>
</p> 
</div>
</section>

<section align="center" style="max-width: 896px; margin: 0 auto;">
<div>
<h2 style="color: #138c33">Maintainer</h2>
<a href="https://crnd.pro/"><img alt="CR&amp;D" src="https://crnd.pro/web/image/3699/300x140/crnd.png" /></a>
<p>Our web site: <a href="https://crnd.pro">https://crnd.pro</a>
</p>
<p>This module is maintained by the <a href="https://crnd.pro/">Center of Research &amp; Development</a>
company.
</p>
<p>
We can provide you further Odoo Support, Odoo implementation, Odoo customization, Odoo 3rd Party development and integration software, consulting services. Our main goal is to provide the best quality product for you. 
</p>
<p>
For any questions <a href="mailto:<EMAIL>">contact us</a>.
</p>
</div>
</section>
