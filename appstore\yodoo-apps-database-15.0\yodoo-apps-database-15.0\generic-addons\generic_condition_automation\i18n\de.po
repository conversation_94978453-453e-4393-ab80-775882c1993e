# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_condition_automation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-20 12:35+0000\n"
"PO-Revision-Date: 2020-10-20 12:35+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: generic_condition_automation
#: model:ir.model,name:generic_condition_automation.model_base_automation
msgid "Automated Action"
msgstr ""

#. module: generic_condition_automation
#: model:base.automation,name:generic_condition_automation.test_rule_on_write
#: model:ir.actions.server,name:generic_condition_automation.test_rule_on_write_ir_actions_server
msgid "Generic Condition: Test rule on write"
msgstr ""

#. module: generic_condition_automation
#: model:generic.condition,name:generic_condition_automation.demo_condition_partner_city_not_kyiv
msgid "Not (city: Kyiv)"
msgstr ""

#. module: generic_condition_automation
#: model:ir.model.fields,field_description:generic_condition_automation.field_base_automation__post_condition_ids
msgid "Post Conditions"
msgstr ""

#. module: generic_condition_automation
#: model:ir.model.fields,help:generic_condition_automation.field_base_automation__post_condition_ids
msgid "Post conditions (Generic conditions)"
msgstr ""

#. module: generic_condition_automation
#: model:ir.model.fields,field_description:generic_condition_automation.field_base_automation__pre_condition_ids
msgid "Pre Conditions"
msgstr ""

#. module: generic_condition_automation
#: model:ir.model.fields,help:generic_condition_automation.field_base_automation__pre_condition_ids
msgid "Pre conditions (Generic conditions)"
msgstr ""

#. module: generic_condition_automation
#: model_terms:ir.ui.view,arch_db:generic_condition_automation.view_base_automation_form
msgid "Set filter conditions:"
msgstr ""

#. module: generic_condition_automation
#: model_terms:ir.ui.view,arch_db:generic_condition_automation.view_base_automation_form
msgid "Set search conditions:"
msgstr ""
