# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* generic_mixin
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-13 15:06+0000\n"
"PO-Revision-Date: 2023-07-26 09:29+0000\n"
"Last-Translator: Den_<PERSON>haraf <<EMAIL>>\n"
"Language-Team: Ukrainian <http://weblate.crnd.pro/projects/generic-"
"addons-15-0/generic_mixin/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__active
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__active
msgid "Active"
msgstr "Активно"

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__archived
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__archived
msgid "Archived"
msgstr "Архівовано"

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_no_unlink.py:0
#, python-format
msgid ""
"Cannot unlink these records. Operation not allowed.\n"
"It is better to deactivate these records.\n"
"Model: %(model)s [%(model_name)s]"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_no_unlink.py:0
#, python-format
msgid ""
"Cannot unlink these records. Operation not allowed.\n"
"Model: %(model)s [%(model_name)s]"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_name_with_code__code
msgid "Code"
msgstr "Код"

#. module: generic_mixin
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_location_type_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_mixin_name_with_code_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_resource_type_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_group_code_ascii_only
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_level_code_ascii_only
msgid "Code must be ascii only"
msgstr "Код має бути лише у форматі ASCII"

#. module: generic_mixin
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_location_type_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_mixin_uniq_name_code_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_resource_type_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_group_code_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_level_code_uniq
msgid "Code must be unique."
msgstr "Код повинен бути унікальним."

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_id
msgid "Data record"
msgstr "Запис даних"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_ids
msgid "Data records"
msgstr "Записи даних"

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_guard_fields.py:0
#, python-format
msgid ""
"Direct modification of '%(model)s:%(field_name)s' field is is not allowed!"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__draft
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__draft
msgid "Draft"
msgstr "Драфт"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__email
msgid "Email"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_parent.py:0
#, python-format
msgid "Error ! You cannot create recursive %s."
msgstr "Помилка ! Ви не можете створювати рекурсивні записи %s."

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_delegation_interface.py:0
#, python-format
msgid "Error: unknown implementation"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_delegation_implementation
msgid "Generic Mixin Delegation: Implementation"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_delegation_interface
msgid "Generic Mixin Delegation: Interface"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_contact
msgid "Generic Mixin: Contacts"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_entity_lifecycle
msgid "Generic Mixin: Entity Lifecycle"
msgstr "Generic Mixin: життєвий цикл сутності"

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_get_action
msgid "Generic Mixin: Get Action"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_guard_fields
msgid "Generic Mixin: Guard Fields"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_name_by_sequence
msgid "Generic Mixin: Name by Sequence"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_namesearch_by_fields
msgid "Generic Mixin: Name search by fields"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_name_with_code
msgid "Generic Mixin: Name with code"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_no_unlink
msgid "Generic Mixin: No Unlink"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_parent_names
msgid "Generic Mixin: Parent Names"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_proxy_methods
msgid "Generic Mixin: Proxy Methods"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_refresh_view
msgid "Generic Mixin: Refresh view"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_track_changes
msgid "Generic Mixin: Track Changes"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_uuid
msgid "Generic Mixin: UUID"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_uniq_name_code
msgid "Generic Mixin: Unique name and code"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_data_updatable
msgid "GenericMixin: Data Updatable"
msgstr ""

#. module: generic_mixin
#: model:ir.model,name:generic_mixin.model_generic_mixin_transaction_utils
msgid "GenericMixin: Transaction Utils"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,help:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_no_update
msgid ""
"Indicates whether this record will be updated with module update or not. If "
"set to True, the record will not be overriden on module update, if set to "
"False, thenrecord will be overridden by module update"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_contact.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_entity_lifecycle.py:0
#, python-format
msgid ""
"It is not allowed to change Lifecycle State fieldfrom %(old_state)s to "
"%(new_state)s!\n"
"Allowed next states: %(allowed_states)s"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_activated
msgid "Lifecycle Date Activated"
msgstr "Дата життєвого циклу активовано"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_archived
msgid "Lifecycle Date Archived"
msgstr "Дата життєвого циклу в архіві"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_created
msgid "Lifecycle Date Created"
msgstr "Дата створення життєвого циклу"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_date_obsolete
msgid "Lifecycle Date Obsolete"
msgstr "Дата Закінчення Життєвого циклу"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_entity_lifecycle__lifecycle_state
msgid "Lifecycle State"
msgstr "Стан життєвого циклу"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_fb
msgid "Link Fb"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_instagram
msgid "Link Instagram"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_linkedin
msgid "Link Linkedin"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_telegram
msgid "Link Telegram"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_twitter
msgid "Link Twitter"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_viber
msgid "Link Viber"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_whatsapp
msgid "Link Whatsapp"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__link_youtube
msgid "Link Youtube"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_city
msgid "Location City"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_country_id
msgid "Location Country"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_state_id
msgid "Location State"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_street
msgid "Location Street"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_street2
msgid "Location Street2"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__location_zip
msgid "Location Zip"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_name_with_code__name
msgid "Name"
msgstr "Назва"

#. module: generic_mixin
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_location_type_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_mixin_uniq_name_code_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_resource_type_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_group_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_level_name_uniq
#: model:ir.model.constraint,message:generic_mixin.constraint_generic_service_name_uniq
msgid "Name must be unique."
msgstr "Ім'я має бути унікальним."

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_name_by_sequence.py:0
#, python-format
msgid "New"
msgstr "Новий"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_no_update
msgid "Non Updatable"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_mixin_entity_lifecycle__lifecycle_state__obsolete
#: model:ir.model.fields.selection,name:generic_mixin.selection__generic_service__lifecycle_state__obsolete
msgid "Obsolete"
msgstr "Застарілий"

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__phone
msgid "Phone"
msgstr ""

#. module: generic_mixin
#: code:addons/generic_mixin/models/generic_mixin_contact.py:0
#, python-format
msgid "Telegram link must start with 'https://t.me/'"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_contact__website_link
msgid "Website Link"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,field_description:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_xmlid
msgid "XML ID"
msgstr ""

#. module: generic_mixin
#: model:ir.model.fields,help:generic_mixin.field_generic_mixin_data_updatable__ir_model_data_xmlid
msgid "XML ID for this record."
msgstr ""

#~ msgid "Display Name"
#~ msgstr "Назва для відображення"

#~ msgid "Last Modified on"
#~ msgstr "Остання модифікація"

#~ msgid ""
#~ "Cannot unlink these records. Operation not allowed.\n"
#~ "It is better to deactivate these records.\n"
#~ "Model: %s [%s]"
#~ msgstr ""
#~ "Не можливо видалити ці записи. Операція не дозволена.\n"
#~ "Краще деактивуйте ці записи.\n"
#~ "Модель: %s [%s]"

#~ msgid ""
#~ "Cannot unlink these records. Operation not allowed.\n"
#~ "Model: %s [%s]"
#~ msgstr ""
#~ "Не можливо видалити ці записи. Операція не дозволена.\n"
#~ "Модель: %s [%s]"
