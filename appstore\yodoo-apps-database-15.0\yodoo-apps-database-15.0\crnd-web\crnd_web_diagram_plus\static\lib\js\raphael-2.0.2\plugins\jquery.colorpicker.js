/*!
 * Color Picker 0.1.0 - Raphael plugin
 *
 * Copyright (c) 2010 <PERSON> (http://raphaeljs.com)
 * Based on Color Wheel (http://jweir.github.com/colorwheel) by <PERSON> (http://famedriver.com)
 * Licensed under the MIT (http://www.opensource.org/licenses/mit-license.php) license.
 */
(function ($, R) {
    $.fn.colorpicker = function (size, initcolor) {
        if (R) {
            var offset = this.offset();
            return <PERSON>.colorpicker(offset.left, offset.top, size, initcolor, this[0]);
        }
        return null;
    };
})(window.jQuery, window.Raphael);