<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--Creating parent location-->

    <record id="simple_parent_location_1" model="generic.location">
        <field name='name'>House</field>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_parent_location_2" model="generic.location">
        <field name='name'>Hostel</field>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_parent_location_3" model="generic.location">
        <field name='name'>Hostel-2</field>
    </record>
    <record id="simple_parent_location_4" model="generic.location">
        <field name='name'>Hostel-3</field>
    </record>
    <record id="simple_parent_location_5" model="generic.location">
        <field name='name'>Hostel-4</field>
    </record>
    <record id="simple_parent_location_6" model="generic.location">
        <field name='name'>Hostel-5</field>
    </record>

    <!--Creating simple location-->
    <record id="simple_location_room_1" model="generic.location">
        <field name="name">Room 1</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_2" model="generic.location">
        <field name="name">Room 2</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_3" model="generic.location">
        <field name="name">Room 3</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_4" model="generic.location">
        <field name="name">Room 4</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_5" model="generic.location">
        <field name="name">Room 5</field>
        <field name="parent_id" ref="simple_parent_location_2"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_6" model="generic.location">
        <field name="name">Room 6</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_7" model="generic.location">
        <field name="name">Room 7</field>
        <field name="parent_id" ref="simple_parent_location_3"/>
    </record>
    <record id="simple_location_room_8" model="generic.location">
        <field name="name">Room 8</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_9" model="generic.location">
        <field name="name">Room 9</field>
        <field name="parent_id" ref="simple_parent_location_4"/>
    </record>
    <record id="simple_location_room_10" model="generic.location">
        <field name="name">Room 10</field>
        <field name="parent_id" ref="simple_parent_location_5"/>
    </record>
    <record id="simple_location_room_11" model="generic.location">
        <field name="name">Room 11</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_12" model="generic.location">
        <field name="name">Room 12</field>
        <field name="parent_id" ref="simple_parent_location_6"/>
    </record>
    <record id="simple_location_room_13" model="generic.location">
        <field name="name">Room 13</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_14" model="generic.location">
        <field name="name">Room 14</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_21" model="generic.location">
        <field name="name">Room 2</field>
        <field name="parent_id" ref="simple_parent_location_6"/>
    </record>
    <record id="simple_location_room_51" model="generic.location">
        <field name="name">Room 5</field>
        <field name="parent_id" ref="simple_parent_location_5"/>
    </record>
    <record id="simple_location_room_61" model="generic.location">
        <field name="name">Room 6</field>
        <field name="parent_id" ref="simple_parent_location_2"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_71" model="generic.location">
        <field name="name">Room 7</field>
        <field name="parent_id" ref="simple_parent_location_6"/>
    </record>
    <record id="simple_location_room_81" model="generic.location">
        <field name="name">Room 8</field>
        <field name="parent_id" ref="simple_parent_location_3"/>
    </record>
    <record id="simple_location_room_91" model="generic.location">
        <field name="name">Room 9</field>
        <field name="parent_id" ref="simple_parent_location_2"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_101" model="generic.location">
        <field name="name">Room 10</field>
        <field name="parent_id" ref="simple_parent_location_1"/>
        <field name="partner_id" ref="base.res_partner_2"/>
    </record>
    <record id="simple_location_room_111" model="generic.location">
        <field name="name">Room 11</field>
        <field name="parent_id" ref="simple_parent_location_3"/>
    </record>

    <!-- location with miltiple siblocations -->
    <record id="simple_location_root_1" model="generic.location">
        <field name="name">Root</field>
    </record>
    <record id="simple_location_room_1_l1_b1" model="generic.location">
        <field name="name">L1 Branch 1</field>
        <field name="parent_id" ref="simple_location_root_1"/>
    </record>
    <record id="simple_location_room_1_l1_b2" model="generic.location">
        <field name="name">L1 Branch 2</field>
        <field name="parent_id" ref="simple_location_root_1"/>
    </record>
    <record id="simple_location_room_1_l2_b1_1" model="generic.location">
        <field name="name">L2 Branch 1</field>
        <field name="parent_id" ref="simple_location_room_1_l1_b1"/>
    </record>
    <record id="simple_location_room_1_l2_b1_2" model="generic.location">
        <field name="name">L2 Branch 2</field>
        <field name="parent_id" ref="simple_location_room_1_l1_b1"/>
    </record>
    <record id="simple_location_room_1_l2_b2_1" model="generic.location">
        <field name="name">L2 Branch 3</field>
        <field name="parent_id" ref="simple_location_room_1_l1_b2"/>
    </record>
    <record id="simple_location_room_1_l3_b1_1" model="generic.location">
        <field name="name">L3 Branch 1</field>
        <field name="parent_id" ref="simple_location_room_1_l2_b1_2"/>
    </record>

    <!-- Locations with addresses -->
    <record id="demo_location_country_us" model="generic.location">
        <field name="name">US</field>
        <field name="country_id" ref="base.us"/>
        <field name="street_use_parent" eval="False"/>
        <field name="street2_use_parent" eval="False"/>
        <field name="city_use_parent" eval="False"/>
        <field name="zip_use_parent" eval="False"/>
        <field name="state_id_use_parent" eval="False"/>
        <field name="country_id_use_parent" eval="False"/>
    </record>

    <record id="demo_location_state_us_ny" model="generic.location">
        <field name="name">New York</field>
        <field name="parent_id" ref="demo_location_country_us"/>
        <field name="state_id" ref="base.state_us_27"/>
        <field name="street_use_parent" eval="True"/>
        <field name="street2_use_parent" eval="True"/>
        <field name="city_use_parent" eval="True"/>
        <field name="zip_use_parent" eval="True"/>
        <field name="state_id_use_parent" eval="False"/>
        <field name="country_id_use_parent" eval="True"/>
    </record>
    <record id="demo_location_city_us_ny_buffalo" model="generic.location">
        <field name="name">Buffalo</field>
        <field name="parent_id" ref="demo_location_state_us_ny"/>
        <field name="city">Buffalo</field>
        <field name="street_use_parent" eval="True"/>
        <field name="street2_use_parent" eval="True"/>
        <field name="city_use_parent" eval="False"/>
        <field name="zip_use_parent" eval="True"/>
        <field name="state_id_use_parent" eval="True"/>
        <field name="country_id_use_parent" eval="True"/>
    </record>
    <record id="demo_location_street_us_ny_buffalo_4371_bottom_lane" model="generic.location">
        <field name="name">4371 Bottom Lane</field>
        <field name="parent_id" ref="demo_location_city_us_ny_buffalo"/>
        <field name="street_use_parent" eval="False"/>
        <field name="street2_use_parent" eval="False"/>
        <field name="city_use_parent" eval="True"/>
        <field name="zip_use_parent" eval="False"/>
        <field name="state_id_use_parent" eval="True"/>
        <field name="country_id_use_parent" eval="True"/>
    </record>
</odoo>
